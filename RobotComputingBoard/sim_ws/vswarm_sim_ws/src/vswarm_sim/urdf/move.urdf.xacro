<robot name="mycar" xmlns:xacro="http://wiki.ros.org/xacro">
 
    <!-- 传动实现:用于连接控制器与关节 -->
    <xacro:macro name="joint_trans" params="joint_name">
        <!-- Transmission is important to link the joints and the controller -->
        <transmission name="${joint_name}_trans">
            <type>transmission_interface/SimpleTransmission</type>
            <joint name="${joint_name}">
                <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
            </joint>
            <actuator name="${joint_name}_motor">
                <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
                <mechanicalReduction>1</mechanicalReduction>
            </actuator>
        </transmission>
    </xacro:macro>
 
    <!-- 每一个驱动轮都需要配置传动装置 -->
    <xacro:joint_trans joint_name="Wheel_left1" />
    <xacro:joint_trans joint_name="Wheel_left2" />
    <xacro:joint_trans joint_name="Wheel_right1" />
    <xacro:joint_trans joint_name="Wheel_right2" />
 
    <!-- 麦轮控制器 -->
    <gazebo>
        <plugin name="mecanum_controller" filename="libgazebo_ros_planar_move.so">
            <commandTopic>robot/velcmd</commandTopic>
            <odometryTopic>robot/odom</odometryTopic>
            <odometryFrame>odom_frame</odometryFrame>
            <leftFrontJoint>Wheel_left1</leftFrontJoint>
            <rightFrontJoint>Wheel_left2</rightFrontJoint>
            <leftRearJoint>Wheel_right1</leftRearJoint>
            <rightRearJoint>Wheel_right2</rightRearJoint>
            <odometryRate>100</odometryRate>
            <robotBaseFrame>base_footprint</robotBaseFrame>
            <broadcastTF>1</broadcastTF>
        </plugin>
    </gazebo>
 
</robot>
