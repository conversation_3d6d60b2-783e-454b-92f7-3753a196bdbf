<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="example">
  <!--
  <xacro:arg name="gpu" default="false"/>
  <xacro:property name="gpu" value="$(arg gpu)" />
  <xacro:arg name="organize_cloud" default="false"/>
  <xacro:property name="organize_cloud" value="$(arg organize_cloud)" />
  -->

  <!-- Base Footprint -->
  <!-- <link name="base_footprint" /> -->

  <!-- Base Link -->
  <!--
  <joint name="footprint" type="fixed" >
   <parent link="base_footprint" />
    <child link="base_link" />
    <origin xyz="0 0 0.05" rpy="0 0 0" />
  </joint>
  <link name="base_link" >
    <visual>
      <geometry>
        <box size="0.5 0.5 0.1" />
      </geometry>
    </visual>
    <collision>
      <geometry>
        <box size="0.5 0.5 0.1" />
      </geometry>
    </collision>
    <inertial>
      <origin xyz="0 0 0"/>
      <mass value="10"/>
      <inertia ixx="3.0" ixy="0.0" ixz="0.0"
               iyy="3.0" iyz="0.0" 
               izz="3.0" />
    </inertial>
  </link>
  -->

  <xacro:include filename="$(find vswarm_sim)/urdf/agent_nocam.urdf" />
  <!--<xacro:include filename="$(find vswarm_sim)/urdf/move.urdf.xacro" />
  <xacro:include filename="$(find vswarm_sim)/urdf/camera.urdf.xacro" />
-->

<!--
  <xacro:include filename="$(find velodyne_description)/urdf/HDL-32E.urdf.xacro"/>
  <xacro:HDL-32E parent="base_link" name="velodyne2" topic="/velodyne_points2" organize_cloud="${organize_cloud}" hz="10" samples="220" gpu="${gpu}">
    <origin xyz="0 0 0.6" rpy="0 0 0" />
  </xacro:HDL-32E>
!-->
</robot>
