#!/usr/bin/python3.8
import rospy
from idPD import idPD
from nav_msgs.msg import Odometry
from geometry_msgs.msg import PoseStamped, Twist

alpha = 0.5

x=idPD(P=0.9, D=2.3, scal=0.15*alpha, alpha=0.1)
y=idPD(P=0.9, D=2.3, scal=0.15*alpha, alpha=0.1)

rospy.init_node('point_ctrl',anonymous=True)

#global x_goal, y_goal
x_goal = 1.0
y_goal = 1.0

def pos_callback(msg):
    global x_goal, y_goal
    x_real = msg.pose.pose.position.x
    y_real = msg.pose.pose.position.y
    diff_x = x_goal - x_real
    diff_y = y_goal - y_real
    sp_x = x.ctrl(diff_x)
    sp_y = x.ctrl(diff_y)
    if (diff_x **2 + diff_y **2 > 0.3):
        print(f"**moving**\n x: {diff_x}\ny :{diff_y}")
    # print(sp_x, sp_y)
    motion_cmd = Twist()
    motion_cmd.linear.x = sp_x
    motion_cmd.linear.y = sp_y
    motion_cmd.angular.z = 0
    vel_pub.publish(motion_cmd)
    
def goal_callback(msg):
    global x_goal, y_goal
    x_goal = msg.pose.position.x
    y_goal = msg.pose.position.y
    

# def getPoint():
#     #tf_linster.get_transformation()
#     tfpose = tf_linster.transform_pose(map_pose.pose)
#     point = tfpose.pose.position
#     qutar = tfpose.pose.orientation

#     x = point.x
#     y = point.y
#     z = point.z
#     yaw =  math.atan2(2*(qutar.w*qutar.z+qutar.x*qutar.y),1-2*(qutar.z*qutar.z+qutar.y*qutar.y))
#     yaw = yaw*180/math.pi
#     return x,y,z,yaw,tfpose.pose



#while not rospy.is_shutdown():
    #err_x, err_y = getPoint()
    #if wait_point ==1:
    
if __name__ == "__main__":
    rospy.Subscriber("/robot/odom",Odometry,pos_callback)
    rospy.Subscriber("/move_base_simple/goal", PoseStamped, goal_callback)

    vel_pub = rospy.Publisher('robot/velcmd', Twist, queue_size=10)
    while not rospy.is_shutdown():
        pass
    rospy.spin()