#! /usr/bin/env python3
import threading
import time
import socket
import json
import os
import cmd
import urllib.request
import base64

import rospy
from gazebo_msgs.srv import SpawnModel
from geometry_msgs.msg import Pose
from gazebo_msgs.msg import ModelState, ModelStates

from paho.mqtt import client as mqtt_client

broker = '*********'  # mqtt代理服务器地址
port = 1883
keepalive = 60     # 与代理通信之间允许的最长时间段（以秒为单位）              

username = '2aef61458447695d'
password = '9AuA9BMIF9ClGrar1krGz4bmy19CvZyBZCsN3ePIUUcRSaG'

client_id = f'{socket.gethostname()}' 

class mqtt_client_thread():
    def __init__(self, broker, port, keepalive, client_id):
        super(mqtt_client_thread, self).__init__()
        self.broker = broker  # mqtt代理服务器地址
        self.port = port
        self.keepalive = keepalive 
        self.client_id = client_id
        self.recv_topic = '/simserver/recv'
        self.send_topic = '/simserver/send'
        self.reg_topic = '/simserver/register'
        self.agent_dict = {}
        
        self.client = self.connect_mqtt()
        self.client.on_message = self.mqtt_callback
        
    def publish(self, topic, msg):
        result = self.client.publish(topic, msg)
        status = result[0]
        if status == 0:
            pass
            #print(f"Send `{msg}` to topic `{topic}`")
        else:
            print(f"Failed to send message to topic {topic}")  
                  
    def get_agent(self):
        client_url = f'http://{broker}:18083/api/v5/clients'
        req = urllib.request.Request(client_url)
        req.add_header('Content-Type', 'application/json')

        auth_header = "Basic " + base64.b64encode((username + ":" + password).encode()).decode()
        req.add_header('Authorization', auth_header)

        with urllib.request.urlopen(req) as response:
            client_data = json.loads(response.read().decode())
        
        agent_list = []
        for client in client_data['data']:
            if '_robot' in client['clientid']:
                agent_list.append(f"/{client['clientid']}")
        return agent_list
       
    def set_subscribe(self):
        self.client.subscribe('/simserver/send') 
        
    def connect_mqtt(self):
        '''连接mqtt代理服务器'''
        def on_connect(client, userdata, flags, rc):
            '''连接回调函数'''
            # 响应状态码为0表示连接成功
            if rc == 0:
                print("Connected to MQTT OK!")
            else:
                print("Failed to connect, return code %d", rc)
        # 连接mqtt代理服务器，并获取连接引用
        client = mqtt_client.Client(self.client_id)
        client.on_connect = on_connect
        client.connect(self.broker, self.port, self.keepalive)
        return client          
    def mqtt_callback(self, client, userdata, msg):
        '''订阅消息回调函数'''
        if msg.topic == self.send_topic:
            rev_msg = json.loads(msg.payload.decode())
            self.agent_dict = rev_msg
                
        pass
    def run(self):
        self.client.loop_forever()
        

        
            

            
class user_cmd_input(cmd.Cmd):
    intro = ">>Welcome to the CLI. Type 'help' to list commands."
    prompt = ">>"
    def __init__(self, client):
        super(user_cmd_input, self).__init__()
        self.client = client
                        
    def do_ls(self, args):
        """\n 查看在线设备\n-t:显示订阅话题\n-l:显示设备功能\n"""
        self.agent_list = self.client.get_agent()
        for i, agent in enumerate(self.agent_list):
            print(f"{i}.{agent[1:]}")
        
    def do_quit(self, arg):
        """Exit the CLI."""
        print("Exiting...")
        os._exit(0)
    
    def do_msg(self, arg):
        print(self.client.agent_dict)
            
    #空发送时执行命令
    def emptyline(self):
        return
    #找不到指令
    def default(self, line):
        self.stdout.write('%s:not found\n'%line)
        
        
class gazebo_node_thread():
    def __init__(self, client):
        # try:
            rospy.init_node('sim_robot', anonymous=True)
            self.mqtt_client = client
            self.set_model_states = rospy.Publisher('/gazebo/set_model_state', ModelState, queue_size=10)
            print("Connected to ROS OK!")
            self.model_list = []
        # except:
        #     print("Connected to ROS Failed!")
        # pass
    def get_model_states(self, msg):
        mycarIndex = msg.name.index(self.model_name)
        carPos = msg.pose[mycarIndex]

        self.px = carPos.position.x
        self.py = carPos.position.y
        self.pz = carPos.position.z
        self.qx = carPos.orientation.x
        self.qy = carPos.orientation.y
        self.qz = carPos.orientation.z
        self.qw = carPos.orientation.w
    
    def sim_init(self, pos):
        # 获取/robot_description参数的值
        robot_description = rospy.get_param('/robot_description')
        # 模型名称和命名空间
        model_name = client_id
        model_namespace = ''
        # 初始姿态
        initial_pose = Pose()
        initial_pose.position.x = pos['pos_x']
        initial_pose.position.y = pos['pos_y']
        initial_pose.position.z = pos['pos_z']

        self.spawn_model(robot_description, model_name, model_namespace, initial_pose) 
        #rospy.set_param('model_name', str(model_name))
        #rospy.set_param('swarm_list', str(model_name))

        #modelName = rospy.get_param('model_name')
        #model_list = rospy.get_param('swarm_list')
        #print(model_list)
        return model_name
    
    def add_agent(self, pos, id_):
        # 获取/robot_description参数的值
        robot_description = rospy.get_param('/robot_nocam_description')
        # 模型名称和命名空间
        model_name = id_
        model_namespace = id_
        # 初始姿态
        initial_pose = Pose()
        initial_pose.position.x = pos['pos_x']
        initial_pose.position.y = pos['pos_y']
        initial_pose.position.z = pos['pos_z']

        self.spawn_model(robot_description, model_name, model_namespace, initial_pose) 
        #rospy.set_param('model_name', str(model_name))
        #rospy.set_param('swarm_list', str(model_name))

        #modelName = rospy.get_param('model_name')
        #model_list = rospy.get_param('swarm_list')
        #print(model_list)
        return model_name
    
    def spawn_model(self, model_xml, model_name, model_namespace, initial_pose):
        rospy.wait_for_service('/gazebo/spawn_urdf_model')
        try:
            spawn_model_prox = rospy.ServiceProxy('/gazebo/spawn_urdf_model', SpawnModel)
            spawn_model_prox(model_name, model_xml, model_namespace, initial_pose, "world")
            rospy.loginfo("Spawned model: %s", model_name)
        except rospy.ServiceException as e:
            rospy.logerr("Spawn model service call failed: %s", e)

    def run(self):
        #初始化注册
        reg_msg = {
            'live': True,
            'agent': client_id,
        } 
        reg_state = False
        self.mqtt_client.publish(self.mqtt_client.reg_topic, json.dumps(reg_msg))
        ros_hz = 50
        rate = rospy.Rate(ros_hz)
        while not rospy.is_shutdown():
            try:
                if (not reg_state) and (client_id in self.mqtt_client.agent_dict):
                    reg_state = True
                    self.model_name = self.sim_init(self.mqtt_client.agent_dict[client_id])
                    rospy.Subscriber("/gazebo/model_states",ModelStates,self.get_model_states,queue_size=10)
                if reg_state:
                    for each in self.mqtt_client.agent_dict:
                        if (each not in self.model_list) and (each != client_id):
                            self.model_list.append(each)
                            self.add_agent(self.mqtt_client.agent_dict[each], each)
                    try:
                        rev_msg = {
                        'data_type': 'transform',
                        'agent': client_id,
                        'args':{'pos_x':self.px,
                                'pos_y':self.py,
                                'pos_z':self.pz,
                                'q_x':self.qx,
                                'q_y':self.qy, 
                                'q_z':self.qz, 
                                'q_w':self.qw       
                                }
                        } 
                        self.mqtt_client.publish(self.mqtt_client.recv_topic, json.dumps(rev_msg))
                    except:
                        pass
                    try:
                        for each in self.model_list:
                            tmp_model_state = ModelState()
                            tmp_model_state.model_name = each
                            tmp_model_state.pose.position.x = self.mqtt_client.agent_dict[each]['pos_x']
                            tmp_model_state.pose.position.y = self.mqtt_client.agent_dict[each]['pos_y']
                            tmp_model_state.pose.position.z = self.mqtt_client.agent_dict[each]['pos_z']
                            tmp_model_state.pose.orientation.x = self.mqtt_client.agent_dict[each]['q_x']
                            tmp_model_state.pose.orientation.y = self.mqtt_client.agent_dict[each]['q_y']
                            tmp_model_state.pose.orientation.z = self.mqtt_client.agent_dict[each]['q_z']
                            tmp_model_state.pose.orientation.w = self.mqtt_client.agent_dict[each]['q_w']
                            
                            self.set_model_states.publish(tmp_model_state)
                    except:
                        pass                    
                #print(self.mqtt_client.agent_dict)
                rate.sleep()
            except rospy.ROSInterruptException:
                rospy.logerr("ROS Interrupt Exception! Just ignore the exception!")
            except rospy.ROSTimeMovedBackwardsException:
                rospy.logerr("ROS Time Backwards! Just ignore the exception!")
            
           

if __name__ == "__main__":
    
    # 启动MQTT客户端的线程
    mqtt_client_instance = mqtt_client_thread(broker=broker, 
                                           port=port, 
                                           keepalive=keepalive, 
                                           client_id=client_id + '_simrobot'
                                           )
    mqtt_thread = threading.Thread(target=mqtt_client_instance.run)
    mqtt_thread.start()
    client = mqtt_client_instance
    client.set_subscribe()
    time.sleep(1)
    ros_thread_instance = gazebo_node_thread(client=client)
    ros_thread = threading.Thread(target=ros_thread_instance.run)
    ros_thread.start()
    # 启动Cli线程 
    time.sleep(1)
    user_cmd_instance = user_cmd_input(client=client)
    cli_thread = threading.Thread(target=user_cmd_instance.cmdloop)
    cli_thread.start()
