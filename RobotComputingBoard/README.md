# README

## 环境依赖

- ROS1版本`noetic`

  - **串口包**：`sudo apt-get install ros-noetic-serial`
  - **摄像头包**：`sudo apt-get install ros-noetic-usb-cam`

## 目录说明

RobotComputingBoard（板载计算机）

- **docs**
- **bag**：记录数据
- **utils**：工具脚本
  - scripts
  - inference_server
- **sim_ws**：包含仿真环境实验完整的软件包
  - vswarm_sim_ws
- **real_ws**：包含真实环境实验完整的软件包
  - vicon_ws
  - board_com_ws
  - ...
- **users**：用户程序
- **common**
  - robot_server

## **文档链接**

​	 [集群机器人协议文档](./docs/real/集群机器人控制端通信协议.md )：板载计算机通过USB与控制板通信的通信协议及相关。

​	 [集群机器人板载接口](./docs/real/集群机器人板载接口.md )：板载计算机ROS端的操作接口，包括控制指令与传感信息。

    [代码自动部署](./docs/自动部署.md): 用于将代码自动部署到所有在集群中的小车
