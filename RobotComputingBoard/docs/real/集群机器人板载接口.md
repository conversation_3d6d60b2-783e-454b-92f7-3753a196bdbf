# 板载计算机接口

​	板载计算机给出的ROS接口如下，用于控制集群机器人和采集传感数据，主要使用如下几个功能包：

- board_com_ws：板载通信功能包，类似于mavros，启动串口按照协议与底板通信，并发布话题。
- wireless_com_ws：无线通信接口，包含UDP、UWB等通信方式。

## ROS话题

### 板载控制与传感接口

- **/robot/velcmd**：速度控制指令

  ```c++
  //消息格式：geometry_msgs/Vector3
  //代码格式例程：
  msg->x	//x轴方向的速度 m/s
  msg->y	//y轴方向的速度 m/s
  msg->z	//z轴的旋转速度 deg/s
  ```
  
- **/robot/ledup**：上灯带控制，控制个数为最大42。

  ```c++
  //消息格式：std_msgs/UInt32MultiArray0
  //代码格式例程：
  //将UInt32拆分成rgb888+数量的格式
  //代码格式例程：
  uint32_t GetRGB32Data(uint8_t r, uint8_t g, uint8_t b, uint8_t num)
  {
      return uint32_t((uint32_t)num<<24|(uint32_t)g<<16|(uint32_t)r<<8|b);
  }
  
  void RobotCtrl::LedUpCtrl(void)
  {
      uint32_t data;
      uint8_t all_num = 0;
      /*设定了三种颜色 */
      this->ledupmsg_.data.clear();
      data = GetRGB32Data(0xff,0x00,0x00,14);
      this->ledupmsg_.data.push_back(data);
      data = GetRGB32Data(0x00,0xff,0x00,14);
      this->ledupmsg_.data.push_back(data);
      data = GetRGB32Data(0x00,0x00,0xff,14);
      this->ledupmsg_.data.push_back(data);
      for(uint8_t i = 0; i < this->ledupmsg_.data.size(); i++){
          all_num += this->ledupmsg_.data[i]>>24;
          if(all_num > 42){
              ROS_ERROR(" max led1 %d", all_num);
              return;
          }
      }
      this->pub_ledupcmd_.publish(this->ledupmsg_);
  }
  ```
  
- **/robot/leddown**：下灯带控制，原理同上，控制个数为最大89。

- **/robot/imu**：传感器数据

  ```c++
  //消息格式：sensor_msgs/Imu
  //代码格式例程：
  imu_data_msg_->linear_acceleration.x;//x轴加速度
  imu_data_msg_->linear_acceleration.y;//y轴加速度
  imu_data_msg_->linear_acceleration.z;//z轴加速度
  imu_data_msg_->angular_velocity.x;//x轴角速度
  imu_data_msg_->angular_velocity.y;//y轴角速度
  imu_data_msg_->angular_velocity.z;//z轴角速度
  imu_data_msg_->orientation.w;//四元数
  imu_data_msg_->orientation.x;
  imu_data_msg_->orientation.y;
  imu_data_msg_->orientation.z;
  ```

- **/robot/odem**：里程计数据

    ```c++
    //消息格式：nav_msgs/Odometry
    //代码格式例程：
    odometry_msg_->pose.pose.position.x;//世界坐标系位置x mm
    odometry_msg_->pose.pose.position.y;//世界坐标系位置y mm
    odometry_msg_->pose.pose.position.z = 0;
    odometry_msg_->twist.twist.linear.x;//世界坐标系速度x m/s
    odometry_msg_->twist.twist.linear.y;//世界坐标系速度y m/s
    odometry_msg_->twist.twist.angular.z;//旋转速度deg/s
    ```

- **/robot/gps**：GPS数据

  ```c++
  //消息格式：sensor_msgs/NavSatStatus
  代码格式例程：
  gps_msg_->longitude;//经度
  gps_msg_->latitude;//纬度
  gps_msg_->altitude;//高度
  ```

- **/robot/battery**：电池电量

  ```c++
  //消息格式：std_msgs/Int8
  battary_msg_->data; //0-100
  ```

  