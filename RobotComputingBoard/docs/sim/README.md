仿真框架主要由两部分构成，仿真环境和仿真服务器,仿真服务器部分的详细介绍可以参考小车管理系统中仿真服务器的相关文档

**服务器端** 

在使用过程中，需要保证仿真服务器已经运行，如未运行，使用以下命令：
```
cd $(your path of all projects)$/vSWARM/RobotsManagement/sim_server

python server.py
```

**小车端**

- 配置仿真服务器地址

    将utils/scripts/setenv.sh中的REMOTE_SERVER修改为仿真服务器的地址，然后执行
    ```
    source utils/scripts/setenv.sh
    ```

- 启动仿真服务
  
    在仿真服务器运行的情况下，在每台小车的RobotsManagement目录下，输入以下命令：
    ```
    source sim_ws/vswarm_sim_ws/devel/setup.bash
    roslaunch vswarm_sim gazebo.launch
    ```

    即可启动仿真环境，将小车加入到仿真环境中

- 运动测试
  
    使用pr2_teleop的teleop_pr2_keyboard，请确保该rospkg已安装，使用命令：
    ```
    rosrun pr2_teleop teleop_pr2_keyboard
    ```
    通过WSAD和QE完成移动和旋转，X停止运动