# 集群小车自动部署指南

本文档提供了一套步骤，用于将代码自动部署到所有在集群中的小车。

## 步骤 1: 添加小车信息到 Ansible 配置

您需要将每辆小车的信息添加至 `config/hosts` 文件中。格式如下所示：

```ini
[SWARM]
************* ansible_user=nvidia ansible_password=123
# new_IP ansible_user=username ansible_password=password
```

请确保替换 `new_IP`、`username` 和 `password` 为实际的 IP 地址和相应的小车凭据。

## 步骤 2: 使用 Docker Compose 启动容器

在项目根目录下执行以下命令以启动 Docker 容器，并自动执行 Ansible 指令部署代码：

```sh
docker-compose up
```

这将会启动定义在 `docker-compose.yml` 文件中的容器，并且运行 Ansible Playbook 进行部署。

## Ansible Playbook 详解

以下是自动部署过程中用到 Ansible 操作的一个例子：

```yaml
- name: Git Update
  hosts: SWARM
  vars:
    git_repo_path: /home/<USER>/vSWARM/RobotComputingBoard
  tasks:
    - name: 确保 git 安装完毕
      apt:
        name: git
        state: present
      become: yes

    - name: 切换到 main 分支
      command: git checkout main
      args:
        chdir: "{{ git_repo_path }}"

    - name: 拉取最新代码
      command: git pull origin main
      args:
        chdir: "{{ git_repo_path }}"
```

对于每一个任务：

1. 确保 git 已经在集群小车上安装。
2. 切换到项目代码所在的 git 仓库的 `main` 分支。
3. 从远程仓库拉取最新的代码到本地。

确保 `git_repo_path` 变量设置为集群小车上代码仓库的确切路径。

## 完成部署

一旦 Ansible Playbook 运行完毕，代码将被更新到集群中所有指定的小车上。确保所有的小车均已正确连接到网络并且可通过 SSH 访问。

## 注意

在执行 Ansible Playbook 之前，请仔细检查 `config/hosts` 文件中的小车凭据，确保它们是正确和最新的，以避免运行过程中出现权限问题。

如有任何疑问，请联系维护人员 <EMAIL>
