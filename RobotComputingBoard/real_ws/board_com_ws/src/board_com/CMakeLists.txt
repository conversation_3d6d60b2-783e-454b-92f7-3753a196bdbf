cmake_minimum_required(VERSION 3.0.2)
project(board_com)

find_package(catkin REQUIRED COMPONENTS
  serial
  roscpp
  std_msgs
  geometry_msgs
  sensor_msgs
  nav_msgs
  message_generation
)

include_directories(
  include
  /usr/include/eigen3
  ${catkin_INCLUDE_DIRS}
)

add_service_files(
  FILES
  sscmd.srv
)

generate_messages(
  DEPENDENCIES
  std_msgs
)

catkin_package(
  CATKIN_DEPENDS message_runtime
)

add_executable(board_com 
  src/board_com.cc
)
target_link_libraries(board_com
  ${catkin_LIBRARIES}
)


