<?xml version="1.0"?>

<launch>

  <!-- IP and port on Vicon Windows PC -->
  <arg name="datastream_hostport" default="************"/>
  <!-- Fixed vicon world frame -->
  <arg name="vicon_world_frame" default="/vicon/world"/>

	<node pkg="vicon_bridge" type="vicon_bridge" name="vicon" output="screen" >
		<param name="stream_mode" value="ServerPush"/>
    <param name="datastream_hostport" value="$(arg datastream_hostport)"/>
		<param name="tf_ref_frame_id" value="$(arg vicon_world_frame)"/>
	</node>
	<!--
  <node name="cam0" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam0" />
    <param name="image_width" value="1280" />
    <param name="image_height" value="720" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam0" />
    <param name="io_method" value="mmap"/>
  </node>

 <node name="cam1" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam1" />
    <param name="image_width" value="1280" />
    <param name="image_height" value="720" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam1" />
    <param name="io_method" value="mmap"/>
  </node>

<node name="cam2" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam2" />
    <param name="image_width" value="1280" />
    <param name="image_height" value="720" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam2" />
    <param name="io_method" value="mmap"/>
  </node>

 <node name="cam3" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam3" />
    <param name="image_width" value="1280" />
    <param name="image_height" value="720" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam3" />
    <param name="io_method" value="mmap"/>
		</node>
	-->

</launch>
