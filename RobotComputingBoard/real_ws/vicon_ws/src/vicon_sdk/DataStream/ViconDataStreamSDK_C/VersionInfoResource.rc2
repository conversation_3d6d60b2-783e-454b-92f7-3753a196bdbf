#include "ViconDataStreamSDK_CVersion.h"

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION VICONDATASTREAMSDK_C_VERSION_MAJOR,VICONDATASTREAMSDK_C_VERSION_MINOR,VICONDATASTREAMSDK_C_VERSION_POINT,VICONDATASTREAMSDK_C_VERSION_REVISION
 PRODUCTVERSION VICONDATASTREAMSDK_C_VERSION_MAJOR,VICONDATASTREAMSDK_C_VERSION_MINOR,VICONDATASTREAMSDK_C_VERSION_POINT,VICONDATASTREAMSDK_C_VERSION_REVISION
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", VICONDATASTREAMSDK_C_COMPANY_L
            VALUE "FileDescription", "Vicon DataStream SDK for C"
            VALUE "FileVersion", VICONDATASTREAMSDK_C_FULL_VERSION_STRING_L
            VALUE "InternalName", "Vicon DataStream SDK for C"
            VALUE "LegalCopyright", VICONDATASTREAMSDK_C_COPYRIGHT_L
            VALUE "LegalTrademarks", VICONDATASTREAMSDK_C_TRADEMARK_L
            VALUE "OriginalFilename", "ViconDataStreamSDK_C.dll"
            VALUE "ProductName", "Vicon DataStream SDK"
            VALUE "ProductVersion", VICONDATASTREAMSDK_C_FULL_VERSION_STRING_L
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

