#! /usr/bin/env python

PACKAGE='vicon_bridge'
import roslib; roslib.load_manifest(PACKAGE)

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

enum_noise_distribution = gen.enum([
gen.const("NORMAL",     str_t, "normal",    "normal distributed noise."),
gen.const("UNIFORM",    str_t, "uniform",   "uniform distributed noise."), 
],
"An enum to set the noise distribution.")


#       Name                 Type      Reconfiguration level                Description   Default   Min   Max
gen.add("tf_pub_rate",       double_t, 0,                                       "rate at which tf publishes transforms. ", 100.0, 0.0, 250.0)
gen.add("tf_publish",        bool_t,   0,                                       "determine if output will be published.", True)
gen.add("tf_ref_frame",      str_t,    0,                                       "tf reference frame."             , "world")
gen.add("tf_frame_in",       str_t,    0,                                       "tf input frame."     , "in")
gen.add("tf_frame_out",      str_t,    0,                                       "tf output frame."     , "tf_distort/out")
gen.add("delay",             int_t,    0,                                       "delay [ms]. ", 0,    0,   1000)
gen.add("position_scale",    double_t, 0,                                       "scale of the position measurement", 1.0, 0.01, 10.0)
gen.add("noise_type",        str_t,    0,                                       "noise distribution. Standard deviation in case for normal, max. in case for uniform distribution. ", "normal", edit_method=enum_noise_distribution)
gen.add("sigma_roll_pitch",  double_t, 0,                                       "std dev for roll and pitch [deg]. ", 0,    0,   2)
gen.add("sigma_yaw",         double_t, 0,                                       "std dev for yaw [deg]. ", 0,    0,   2)
gen.add("sigma_xy",          double_t, 0,                                       "std dev for x/y [m]. ", 0,    0,   1)
gen.add("sigma_z",           double_t, 0,                                       "std dev for z [m]. ",   0,    0,   1)
gen.add("random_walk_k_xy",  double_t, 0,                                       "gain for random walk disturbance in x/y axis.", 0, 0, 10)
gen.add("random_walk_k_z",   double_t, 0,                                       "gain for random walk disturbance in z axis.", 0, 0, 10)
gen.add("random_walk_sigma_xy",double_t, 0,                                     "std dev for gauss markov process in x/y axis [m].", 0, 0, 20)
gen.add("random_walk_sigma_z",double_t, 0,                                      "std dev for gauss markov process in x/y axis [m].", 0, 0, 20)
gen.add("random_walk_tau_xy",double_t, 0,                                       "time constant for gauss markov process in x/y axis [s].", 0.001, 1, 3000)
gen.add("random_walk_tau_z" ,double_t, 0,                                       "time constant for gauss markov process in z axis [s].", 0.001, 1, 3000)


exit(gen.generate(PACKAGE, "Config", "tf_distort"))
