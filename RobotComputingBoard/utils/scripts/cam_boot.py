import os
import time
import subprocess
import Jetson.GPIO as GPIO

output_pin = 29  

if __name__ == "__main__":
    # #设置pin的输入输出模式并初始化
    GPIO.setwarnings(False)
    GPIO.setmode(GPIO.BOARD)
    #重启摄像头
    GPIO.setup(output_pin, GPIO.OUT, initial=GPIO.LOW)
    time.sleep(5)
    GPIO.setup(output_pin, GPIO.OUT, initial=GPIO.HIGH)
    #查询信息
    os.system("v4l2-ctl --list-devices")
    time.sleep(2)
    ret = os.system("ls -l /dev/video*")
    ret1 = os.system("ls -l /dev/cam*")
    if ret == 0:
        print("\ncamera ready\n")
    else:
        print("\ncamera not ready\n")
        #os.system("reboot")
    #启动摄像头节点
    cmd_camera = ["roslaunch", "usb_cam", "vswarm-quad-360p.launch"]
    pro_camera = subprocess.Popen(cmd_camera)
    time.sleep(5)

    
