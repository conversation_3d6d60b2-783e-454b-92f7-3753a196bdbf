import rospy
from sensor_msgs.msg import Image
import numpy as np
import cv2
from cv_bridge import CvBridge#, CvBridgeError
import os
import sys
import cmd

request = 0
count0 = 0
count1 = 0
count2 = 0
count3 = 0
global key
key = 0



cwd = os.getcwd()
bridge = CvBridge()
capture0_enable = False
capture1_enable = False
capture2_enable = False
capture3_enable = False

def photograph_callback0(msg):
    global request
    global count0
    global key
    global capture0_enable
    if capture0_enable:     
        capture0_enable = False
        # cv_image = bridge.imgmsg_to_cv2(msg,desired_encoding='passthrough')
        

        cv_image = bridge.imgmsg_to_cv2(msg,desired_encoding='passthrough')

        # cv_image = cv2.flip(cv_image, -1)
        b,g,r = cv2.split(cv_image)
        

        cv_image = cv2.merge([r,g,b])
        file_name = 'cam0_frame_{}.jpg'.format(count0)
        save_path = os.path.join(cwd,'cache/front',file_name)
        success = cv2.imwrite(save_path,cv_image)
        if success:
            key = 0
            request = 0
            count0 += 1
        else:
            print("fail")
        pass
        key = None

def photograph_callback1(msg):
    global request
    global count1
    global key
    global capture1_enable
    if capture1_enable:     
        capture1_enable = False
        cv_image = bridge.imgmsg_to_cv2(msg,desired_encoding='passthrough')
            #save image
        # cv_image = cv2.flip(cv_image, -1)
        b,g,r = cv2.split(cv_image)
        cv_image = cv2.merge([r,g,b])

        file_name = 'cam1_frame_{}.jpg'.format(count1)
        save_path = os.path.join(cwd,'cache/left',file_name)
        success = cv2.imwrite(save_path,cv_image)
        if success:
            key = 0
            request = 0
            count1 += 1
        else:
            print("fail")
        pass
        key = None
    
def photograph_callback2(msg):
    global request
    global count2
    global key
    global capture2_enable
    if capture2_enable:     
        capture2_enable = False
        cv_image = bridge.imgmsg_to_cv2(msg,desired_encoding='passthrough')
            #save image
        # cv_image = cv2.flip(cv_image, -1)
        b,g,r = cv2.split(cv_image)
        cv_image = cv2.merge([r,g,b])

        file_name = 'cam2_frame_{}.jpg'.format(count2)
        save_path = os.path.join(cwd,'cache/rear',file_name)
        success = cv2.imwrite(save_path,cv_image)
        if success:
            key = 0
            request = 0
            count2 += 1
        else:
            print("fail")
        pass
        key = None

def photograph_callback3(msg):
    global request
    global count3
    global key
    global capture3_enable
    if capture3_enable:     
        capture3_enable = False
        cv_image = bridge.imgmsg_to_cv2(msg,desired_encoding='passthrough')
            #save image
        # cv_image = cv2.flip(cv_image, -1)
        b,g,r = cv2.split(cv_image)
        cv_image = cv2.merge([r,g,b])

        file_name = 'cam3_frame_{}.jpg'.format(count3)
        save_path = os.path.join(cwd,'cache/right',file_name)
        success = cv2.imwrite(save_path,cv_image)
        if success:
            key = 0
            request = 0
            count3 += 1
        else:
            print("fail")
        pass
        key = None

class user_cmd_input(cmd.Cmd):
    intro = ">>Welcome to the CLI. Type 'help' to list commands."
    prompt = ">>"
    def __init__(self):
        super(user_cmd_input, self).__init__()
        
    def do_quit(self, arg):
        """Exit the CLI."""
        print("Exiting...")
        os._exit(0)
    
    def do_capture(self, args):
        global capture0_enable
        global capture1_enable
        global capture2_enable
        global capture3_enable
        if not args :
            print("Error input")
            return  
        try:
            args = args.split()
            # 关闭所有设备
            if ((args[0] == "0")or(args[0] == "1")or(args[0] == "2")or(args[0] == "3"))and(len(args)==1):
                print("success")
                if args[0] == "0":
                    capture0_enable = True
                if args[0] == "1":
                    capture1_enable = True
                if args[0] == "2":
                    capture2_enable = True
                if args[0] == "3":
                    capture3_enable = True
                pass
            elif (args[0] == "x")and(len(args)==1):
                print("success")
                capture0_enable = True
                capture3_enable = True
                capture2_enable = True
                capture1_enable = True
        except:
            pass

            
    #空发送时执行命令
    def emptyline(self):
        return
    #找不到指令
    def default(self, line):
        self.stdout.write('%s:not found\n'%line)
        
if __name__ == '__main__':

    # input_thread = threading.Thread(target=user_input)
    # input_thread.start()
    
    os.system("mkdir -p cache")
    os.system("cd cache && mkdir -p front && mkdir -p right && mkdir -p rear && mkdir -p left")
    rospy.init_node('camera_controller',anonymous=True)
    rospy.Subscriber('/cam0/image_raw',Image, photograph_callback0)
    rospy.Subscriber('/cam1/image_raw',Image, photograph_callback1)
    rospy.Subscriber('/cam2/image_raw',Image, photograph_callback2)
    rospy.Subscriber('/cam3/image_raw',Image, photograph_callback3)
    mcmd = user_cmd_input()
    while not rospy.is_shutdown():
        mcmd.cmdloop()
    rospy.spin()

   
