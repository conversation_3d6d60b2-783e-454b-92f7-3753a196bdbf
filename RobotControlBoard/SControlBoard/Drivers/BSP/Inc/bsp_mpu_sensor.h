#ifndef __BSP_MPU_SENSOR_H__
#define __BSP_MPU_SENSOR_H__

#include "bsp.h" 


/* 片选引脚定义 需用户设置*/
//#define MPU_CS_GPIO_Port 	GPIOB              
//#define MPU_CS_Pin	    GPIO_PIN_12

/* 中断引脚定义 需用户设置*/
//#define MPU_INT_Pin GPIO_PIN_0
//#define MPU_INT_GPIO_Port GPIOC

/* SPI句柄 需用户设置*/
#define MPU_SPI_HANDLE hspi2

/* 定义延时函数，用于Motion Driver 需用户设置*/
#define MPU_DELAY   HAL_Delay

/* MPU是否使用外部中断读取数据 */
#define MPU_USE_INT 1

/* MPU传感器的器件SPI ID */
#define MPU_SPI_ID		        0X70

/* MPU IIC 地址 */
#define MPU_IIC_ADRESS		    0X68


/* 寄存器定义 */
#define MPU_RA_XG_OFFS_TC       0x00    //[7] PWR_MODE, [6:1] XG_OFFS_TC, [0] OTP_BNK_VLD
#define MPU_RA_YG_OFFS_TC       0x01    //[7] PWR_MODE, [6:1] YG_OFFS_TC, [0] OTP_BNK_VLD
#define MPU_RA_ZG_OFFS_TC       0x02    //[7] PWR_MODE, [6:1] ZG_OFFS_TC, [0] OTP_BNK_VLD
#define MPU_RA_X_FINE_GAIN      0x03    //[7:0] X_FINE_GAIN
#define MPU_RA_Y_FINE_GAIN      0x04    //[7:0] Y_FINE_GAIN
#define MPU_RA_Z_FINE_GAIN      0x05    //[7:0] Z_FINE_GAIN
#define MPU_RA_XA_OFFS_H        0x06    //[15:0] XA_OFFS
#define MPU_RA_XA_OFFS_L_TC     0x07
#define MPU_RA_YA_OFFS_H        0x08    //[15:0] YA_OFFS
#define MPU_RA_YA_OFFS_L_TC     0x09
#define MPU_RA_ZA_OFFS_H        0x0A    //[15:0] ZA_OFFS
#define MPU_RA_ZA_OFFS_L_TC     0x0B
#define MPU_RA_PRODUCT_ID       0x0C    // Product ID Register
#define MPU_RA_XG_OFFS_USRH     0x13    //[15:0] XG_OFFS_USR
#define MPU_RA_XG_OFFS_USRL     0x14
#define MPU_RA_YG_OFFS_USRH     0x15    //[15:0] YG_OFFS_USR
#define MPU_RA_YG_OFFS_USRL     0x16
#define MPU_RA_ZG_OFFS_USRH     0x17    //[15:0] ZG_OFFS_USR
#define MPU_RA_ZG_OFFS_USRL     0x18
#define MPU_RA_SMPLRT_DIV       0x19    /* 采样频率分频器 */
#define MPU_RA_CONFIG           0x1A    /* 配置寄存器 */ 
#define MPU_RA_GYRO_CONFIG      0x1B    /* 陀螺仪配置寄存器 */
#define MPU_RA_ACCEL_CONFIG     0x1C    /* 加速度计配置寄存器 */
#define MPU_RA_FF_THR           0x1D
#define MPU_RA_FF_DUR           0x1E
#define MPU_RA_MOT_THR          0x1F
#define MPU_RA_MOT_DUR          0x20
#define MPU_RA_ZRMOT_THR        0x21
#define MPU_RA_ZRMOT_DUR        0x22
#define MPU_RA_FIFO_EN          0x23    /* FIFO使能寄存器 */
#define MPU_RA_I2C_MST_CTRL     0x24
#define MPU_RA_I2C_SLV0_ADDR    0x25
#define MPU_RA_I2C_SLV0_REG     0x26
#define MPU_RA_I2C_SLV0_CTRL    0x27
#define MPU_RA_I2C_SLV1_ADDR    0x28
#define MPU_RA_I2C_SLV1_REG     0x29
#define MPU_RA_I2C_SLV1_CTRL    0x2A
#define MPU_RA_I2C_SLV2_ADDR    0x2B
#define MPU_RA_I2C_SLV2_REG     0x2C
#define MPU_RA_I2C_SLV2_CTRL    0x2D
#define MPU_RA_I2C_SLV3_ADDR    0x2E
#define MPU_RA_I2C_SLV3_REG     0x2F
#define MPU_RA_I2C_SLV3_CTRL    0x30
#define MPU_RA_I2C_SLV4_ADDR    0x31
#define MPU_RA_I2C_SLV4_REG     0x32
#define MPU_RA_I2C_SLV4_DO      0x33
#define MPU_RA_I2C_SLV4_CTRL    0x34
#define MPU_RA_I2C_SLV4_DI      0x35
#define MPU_RA_I2C_MST_STATUS   0x36
#define MPU_RA_INT_PIN_CFG      0x37    /* 中断/旁路设置寄存器 */
#define MPU_RA_INT_ENABLE       0x38    /* 中断使能寄存器 */
#define MPU_RA_DMP_INT_STATUS   0x39
#define MPU_RA_INT_STATUS       0x3A
#define MPU_RA_ACCEL_XOUT_H     0x3B    /* 加速度值,X轴高8位寄存器 */
#define MPU_RA_ACCEL_XOUT_L     0x3C    /* 加速度值,X轴低8位寄存器 */
#define MPU_RA_ACCEL_YOUT_H     0x3D    /* 加速度值,Y轴高8位寄存器 */
#define MPU_RA_ACCEL_YOUT_L     0x3E    /* 加速度值,Y轴低8位寄存器 */
#define MPU_RA_ACCEL_ZOUT_H     0x3F    /* 加速度值,Z轴高8位寄存器 */
#define MPU_RA_ACCEL_ZOUT_L     0x40    /* 加速度值,Z轴低8位寄存器 */
#define MPU_RA_TEMP_OUT_H       0x41    /* 温度值高八位寄存器 */
#define MPU_RA_TEMP_OUT_L       0x42    /* 温度值低8位寄存器 */
#define MPU_RA_GYRO_XOUT_H      0x43    /* 陀螺仪值,X轴高8位寄存器 */
#define MPU_RA_GYRO_XOUT_L      0x44    /* 陀螺仪值,X轴低8位寄存器 */
#define MPU_RA_GYRO_YOUT_H      0x45    /* 陀螺仪值,Y轴高8位寄存器 */
#define MPU_RA_GYRO_YOUT_L      0x46    /* 陀螺仪值,Y轴低8位寄存器 */
#define MPU_RA_GYRO_ZOUT_H      0x47    /* 陀螺仪值,Z轴高8位寄存器 */
#define MPU_RA_GYRO_ZOUT_L      0x48    /* 陀螺仪值,Z轴低8位寄存器 */
#define MPU_RA_EXT_SENS_DATA_00 0x49
#define MPU_RA_MOT_DETECT_STATUS    0x61
#define MPU_RA_I2C_SLV0_DO			0x63
#define MPU_RA_I2C_SLV1_DO			0x64
#define MPU_RA_I2C_SLV2_DO			0x65
#define MPU_RA_I2C_SLV3_DO			0x66
#define MPU_RA_I2C_MST_DELAY_CTRL   0x67
#define MPU_RA_SIGNAL_PATH_RESET    0x68
#define MPU_RA_MOT_DETECT_CTRL      0x69
#define MPU_RA_USER_CTRL        0x6A    /* 用户控制寄存器 */
#define MPU_RA_PWR_MGMT_1       0x6B    /* 电源管理寄存器1 */
#define MPU_RA_PWR_MGMT_2       0x6C    /* 电源管理寄存器2 */
#define MPU_RA_BANK_SEL         0x6D
#define MPU_RA_MEM_START_ADDR   0x6E
#define MPU_RA_MEM_R_W          0x6F
#define MPU_RA_DMP_CFG_1        0x70
#define MPU_RA_DMP_CFG_2        0x71
#define MPU_RA_FIFO_COUNTH      0x72
#define MPU_RA_FIFO_COUNTL      0x73
#define MPU_RA_FIFO_R_W         0x74
#define MPU_RA_WHO_AM_I         0x75    /* 器件ID寄存器 */


/* 引脚输出定义 */
#define MPU_CS(n)   (n?HAL_GPIO_WritePin(MPU_CS_GPIO_Port,MPU_CS_Pin,GPIO_PIN_SET):HAL_GPIO_WritePin(MPU_CS_GPIO_Port,MPU_CS_Pin,GPIO_PIN_RESET))   /* MPU6500片选引脚 */

/* 函数声明 */
uint8_t MPU_SensorInit(void);
short MPU_GetTemperature(void);
void MPU_Get_Accelerometer(short *ax,short *ay,short *az);
void MPU_Get_Gyroscope(short *gx,short *gy,short *gz);
int MPU_Sensor_SPI_Write(uint8_t sel, uint8_t reg_addr, uint8_t length, uint8_t const *data);
int MPU_Sensor_SPI_Read(uint8_t sel, uint8_t reg_addr,uint8_t length, uint8_t *data) ;
void MPU_Get_MS(unsigned long *time);
void MPU_Sensor_EXTI_CallBack(uint16_t GPIO_Pin);
uint8_t MPU_SensorCheckUpdata(void);

#endif


