#ifndef __GPS_M8N_H__
#define __GPS_M8N_H__

#include <stdint.h>


/* 用户设置参数 */

/* GPS_M8N使用的波特率 可选9600 38400 115200 */
#define GPS_M8N_BORT 115200       
/* GPS_M8N测量间隔/ms */
#define GPS_MEAS_RATE 100       



/* Message starts */
#define SYNC_CHAR1	0xB5
#define SYNC_CHAR2	0x62

/* UBX Class IDs */
#define UBX_CLASS_NAV		0x01    /* Navigation Results: Position, Speed, Time, Acc, Heading, DOP, SVs used */	
#define UBX_CLASS_RXM       0x02    /* Receiver Manager Messages: Satellite Status, RTC Status */
#define UBX_CLASS_INF       0x04    /* Information Messages: Printf-Style Messages, with IDs such as Error, Warning, Notice */
#define UBX_CLASS_ACK       0x05    /* Ack/Nack Messages: as replies to CFG Input Messages */
#define UBX_CLASS_CFG		0x06    /* Configuration Input Messages: Set Dynamic Model, Set DOP Mask, Set Baud Rate, etc. */
#define UBX_CLASS_MON		0x0A    /* Monitoring Messages: Comunication Status, CPU Load, Stack Usage, Task Status */
#define UBX_CLASS_AID		0x0B    /* AssistNow Aiding Messages: Ephemeris, Almanac, other A-GPS data input */
#define UBX_CLASS_TIM		0x0D    /* Timing Messages: Time Pulse Output, Timemark Results */
#define UBX_CLASS_LOG		0x21    /* Logging Messages: Log creation, deletion, info and retrieval */

/* UBX Message IDs	 */
#define UBX_NAV_PVT	    0x07    /* Navigation Position Velocity Time Solution */
#define UBX_NAV_CLOCK	0x22    /* Clock Solution */


#define UBX_CFG_PRT		0x00    /* 波特率设置 */
#define UBX_CFG_MSG	    0x01    /* 消息设置 */
#define UBX_CFG_RTATE	0x08    /* 输出速率设置 */
#define UBX_CFG_CFG		0x09    /* Clear, Save and Load configurations */


#define UBX_CLASS_ID_OFFSET 2   /* Class ID在UBX协议中的偏移 */
#define UBX_MSG_ID_OFFSET   3   /* Message ID在UBX协议中的偏移 */
#define UBX_LENTH_OFFSET    4   /* UBX_CLASS_ID在UBX协议中的偏移 */
#define UBX_PYLOAD_OFFSET   6   /* PYLOAD在UBX协议中的偏移 */


enum
{
    UBLOX_WAIT_SYNC1 = 0,
    UBLOX_WAIT_SYNC2,
    UBLOX_WAIT_CLASS,
    UBLOX_WAIT_ID,
    UBLOX_WAIT_LEN1,
    UBLOX_WAIT_LEN2,
    UBLOX_PAYLOAD,
    UBLOX_CHECK1,
    UBLOX_CHECK2
};


/* 校验和结构体 */
typedef struct 
{
    uint8_t A;  /* 校验和A */
    uint8_t B;  /* 校验和B */
}UBX_CheckSum_t;


typedef struct {
	uint8_t state;
	uint8_t uclass;
	uint8_t id;
	uint16_t length;
	uint16_t count;
	uint8_t ckA,ckB;
}ublox_t;

typedef struct {
    float   time;		  /* 时间 */
    double	latitude;	  /* 纬度 */
    double	longitude;    /* 经度 */
    float	altitude;	  /* 高度 */
    int32_t	velN;		  /* 北向速度 */
    int32_t	velE;		  /* 东向速度 */
    int32_t	velD;		  /* 天向速度 */
    int32_t speed;        /* 地面速度 */
    float   heading;      /* 航向 */
    float	hAcc;		  /* 水平定位精度 */
    float   vAcc;         /* 垂直定位精度 */
    float   sAcc;         /* 速度精度 */
    float   cAcc;         /* 航向精度 */
    uint8_t fixStatus;    /* 定位状态 */
    uint8_t numSV;		  /* 卫星数量 */
	/* 时间信息 */
	uint16_t   year;
    uint8_t    month;
    uint8_t    day;
    uint8_t    hour;
    uint8_t    min;
    uint8_t    sec;
} ubloxData_t;

typedef struct {
    uint32_t	iTOW;	    /* GPS time of week of the navigation epoch.See the description of iTOW for details.	ms */
    uint16_t	year;		/* Year (UTC) */
    uint8_t		month;		/* Month, range 1..12 (UTC) */
    uint8_t		day;		/* Day of Month, range 1..31 (UTC) */
    uint8_t		hour;		/* Hour of Day, range 0..23 (UTC) */
    uint8_t		min;		/* Minute of Hour, range 0..59 (UTC) */
    uint8_t		sec;		/* Seconds of Minute, range 0..60 (UTC) */
    uint8_t		valid;		/* Validity flags (see graphic below) */
    uint32_t	tAcc;		/* Time accuracy estimate (UTC) */
    int32_t		nano;		/* Fraction of second, range -1e9 .. 1e9 (UTC) */
    uint8_t		gpsFix;		/* GPSfix Type, range 0..5 0: no fix 1: dead reckoning only 2: 2D-fix 3: 3D-fix 4: GNSS + dead reckoning combined 5: time only fix */
    uint8_t		flags;		/* Fix status flags */
    uint8_t		flags2;		/* Additional flags */
    uint8_t		numSV;		/* Number of SVs used in Nav Solution */
    int32_t		lon;		/* Longitude */
    int32_t		lat;		/* Latitude */
    int32_t		height;		/* Height above Ellipsoid */
    int32_t		hMSL;		/* Height above mean sea level */
    uint32_t	hAcc;		/* Horizontal Accuracy Estimate */
    uint32_t	vAcc;		/* Vertical Accuracy Estimate */
    int32_t		velN;		/* NED north velocity cm/s */
    int32_t		velE;		/* NED east velocity	cm/s */
    int32_t		velD;		/* NED down velocity	cm/s */
    int32_t		gSpeed;		/* Ground Speed (2-D) cm/s */
    int32_t		heading;	/* Heading 2-D deg */
    uint32_t	sAcc;		/* Speed Accuracy Estimate cm/s */
    uint32_t	cAcc;		/* Course / Heading Accuracy Estimate deg */
    uint16_t	pDOP;		/* Position DOP */
    uint8_t		reserved1;	/* reserved */
    int32_t		headVeh;	/* Heading of vehicle (2-D) */
    int16_t		magDec;		/* Magnetic declination */
	uint16_t	magAcc;		/* Magnetic declination accuracy */
} ublox_PVT_t;

union ubloxRawData_u
{
	ublox_PVT_t ublox_PVT;
	/* 92位数据 */
	uint8_t PVT[92];
};

/**********************函数声明*************************/
void UBX_Reset(void);
void GPS_UBX_Init(void);
ubloxData_t GPS_UBX_Decode(uint8_t *buff,uint16_t lenth);
ubloxData_t GPS_GetData(void);

#endif


