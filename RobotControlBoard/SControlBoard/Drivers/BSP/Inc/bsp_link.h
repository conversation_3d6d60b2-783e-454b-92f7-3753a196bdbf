#ifndef _BSP_LINK_H__
#define _BSP_LINK_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "stdint.h"
#include <string.h>
#include "stdbool.h"
#include <stdio.h>
#include <stdarg.h> 


/* 功能码定义 */
#define MOVE_MODE_CMD       0x50          /* 移动模式设置指令 */   
#define SPEED_CTL_CMD       0x60          /* 速度控制 */ 
#define UP_RGB_CMD          0x70          /* 上彩灯指令 */
#define DOWN_RGB_CMD        0x71          /* 下彩灯指令 */

#define IMU_MSG             0x10          /* IMU数据 */
#define GPS_MSG             0x28          /* GPS数据 */
#define ODOMETRY_MSG        0x12          /* 里程计数据 */
#define BATTERY_MSG         0x02          /* 电池数据 */
#define VICON_DATA_MSG      0x18          /* VICON数据指令 */
#define HEART_MSG           0x01          /* 心跳包数据 */


/* 基本解析参数 */
typedef struct
{
    uint8_t headLen;
    uint8_t addrLen;

    uint8_t head;
    uint8_t addr;
    uint8_t cmd;
    uint8_t dataLen;
    uint8_t dataBuf[0xFF];
    uint8_t sumCheck;
    uint8_t addCheck;
	/* 总字节数 */
	uint64_t length;
	/* 校验成功与失败次数 */
	uint64_t checkNum[2];
	float errorRate;	
	bool linkState;
	
}link_t;


void Link_Init(void);
void Link_SendLoop(void);
void Link_Receive(uint8_t data);

#ifdef __cplusplus
}
#endif

#endif
