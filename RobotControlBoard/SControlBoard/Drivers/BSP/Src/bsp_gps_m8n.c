/*! ----------------------------------------------------------------------------
 * @file    bsp_gps_m8n.c
 * 
 * @version V1.10 
 *
 * @brief   采用NE0-M8N-0-10的UBX协议与GPS通信
 *           
 *
 *          使用步骤：
 *          1.调用GPS_UBX_Init()函数，需要更改UART5_Init()串口初始化函数和ublox_SendData(h)串口发送函数和UBX_Delay()延时函数
 *          2.在头文件中设置使用波特率及模块输出速率
 *          2.在串口空闲中断中调用GPS_UBX_Decode(uint8_t *buff,uint16_t lenth)或在单个字符串口接收中断中调用ublox_DecodeByte()
 *          3.根据GPS_UBX_Decode()返回值或GPS_GetData()读取GPS数据结构体
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/19    MZ       首版
 *      V1.10   2022/10/24    DSS      增加了通过头文件更改GPS通信波特率和输出速率功能
 *
 * <AUTHOR>
 */
 
#include "bsp_gps_m8n.h"
#include "bsp_uart.h"
#include "usart.h"
#include "bsp_print.h"
#include <string.h>
#include "bsp_lcd_spi.h"
#include "bsp_gps_m8n.h"

/* UBX协议基本格式1.0xB5			2.0x62 		3.class id 		4.message id 
					5.length(2b) 	6.payload	7.checksum (check3~6 2b)*/
                    
                    
static void ublox_PayloadDecode(ublox_PVT_t ublox_PVT);
static void ublox_SendData(const uint8_t *data, uint16_t length);
static void UBX_SetMessageRate(uint8_t uclass, uint8_t id, uint8_t rate);
static void UBX_Delay(uint32_t Delay);
static void UART5_Init(int bort);
static void UBX_SetBortRate(uint32_t bort);
static void UBX_SetMeasRate(uint16_t measRate);
    
static uint8_t ubloxOutBuff[256];
ublox_t ublox;
static ubloxData_t ubloxData;
union ubloxRawData_u ubloxRawData;



/* 设置波特率 */   //                          |cID |mID|  lenth   |U_id| re | txRedeay|        mode       |      bortRate     |inProMask|outPrMask|  flags  | reserve | checksum|
const uint8_t ubloxBaudOutConfig[28]={0xB5,0x62,0x06,0x00,0x14,0x00,0x01,0x00,0x00,0x00,0xD0,0x08,0x00,0x00,0x00,0xC2,0x01,0x00,0x01,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0xB8,0x42};
/* 设置速率 */     //                          |cID |mID|  lenth   |measRate | navRate | timeRef | checksum|
const uint8_t ubloxRateOutConfig[14]={0xB5,0x62,0x06,0x08,0x06,0x00,0x3C,0x00,0x01,0x00,0x01,0x00,0x52,0x22};
/* 复位设置 */     //                          |cID |mID |  lenth   |navBbrMask| resetMode|reserved1| checksum|  
const uint8_t ubloxResetConfig[12] = {0xB5,0x62,0x06,0x04,0x04,0x00, 0x00,0x00,   0x00,      0x00,   0x00,0x00};
 

/**
  * @brief GPS采用UBX协议初始化
  * @param None
  * @retval None
  */
void GPS_UBX_Init(void)
{
    uint32_t ubloxBaud[3]={9600,38400,115200};
    UBX_Reset();
    UBX_Delay(200);
	for(uint8_t i=0;i<3;i++)
	{
		UART5_Init(ubloxBaud[i]);
		UBX_Delay(100);
        UBX_SetBortRate(GPS_M8N_BORT);
        /* 重新设置完波特率后至少延时200ms */
		UBX_Delay(200);
	}
    UART5_Init(GPS_M8N_BORT);
    UBX_Delay(200);
    /* 设置测量速率 10HZ*/
    UBX_SetMeasRate(GPS_MEAS_RATE);
	UBX_Delay(200);	
    
    /* 设置消息输出速率，测量一次输出一次 */
	UBX_SetMessageRate(UBX_CLASS_NAV, UBX_NAV_PVT, 1);
	UBX_Delay(200);
}


/**
  * @brief UBX延时函数
  * @param None
  * @retval None
  */
void UBX_Delay(uint32_t Delay)
{
    HAL_Delay(Delay);
}

/* UART5 init function */
void UART5_Init(int bort)
{
    huart5.Instance = UART5;
    huart5.Init.BaudRate = bort;
    huart5.Init.WordLength = UART_WORDLENGTH_8B;
    huart5.Init.StopBits = UART_STOPBITS_1;
    huart5.Init.Parity = UART_PARITY_NONE;
    huart5.Init.Mode = UART_MODE_TX_RX;
    huart5.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart5.Init.OverSampling = UART_OVERSAMPLING_16;
    huart5.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart5.Init.ClockPrescaler = UART_PRESCALER_DIV1;
    huart5.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_SWAP_INIT|UART_ADVFEATURE_RXOVERRUNDISABLE_INIT
                              |UART_ADVFEATURE_DMADISABLEONERROR_INIT;
    huart5.AdvancedInit.Swap = UART_ADVFEATURE_SWAP_ENABLE;
    huart5.AdvancedInit.OverrunDisable = UART_ADVFEATURE_OVERRUN_DISABLE;
    huart5.AdvancedInit.DMADisableonRxError = UART_ADVFEATURE_DMA_DISABLEONRXERROR;
    if (HAL_UART_Init(&huart5) != HAL_OK)
    {
    Error_Handler();
    }
    if (HAL_UARTEx_SetTxFifoThreshold(&huart5, UART_TXFIFO_THRESHOLD_1_8) != HAL_OK)
    {
    Error_Handler();
    }
    if (HAL_UARTEx_SetRxFifoThreshold(&huart5, UART_RXFIFO_THRESHOLD_1_8) != HAL_OK)
    {
    Error_Handler();
    }
    if (HAL_UARTEx_DisableFifoMode(&huart5) != HAL_OK)
    {
    Error_Handler();
    }
    /* USER CODE BEGIN UART5_Init 2 */
    HAL_UARTEx_ReceiveToIdle_DMA(&huart5, Uart5RxBuffer, USART5_RxBuffer_Size);
    /* USER CODE END UART5_Init 2 */

}

/**
  * @brief 发送数据给ublox模块
  * @param data 需要解析的数据
  * @param length 数据长度
  * @retval None
  */
static void ublox_SendData(const uint8_t *data, uint16_t length)
{
    HAL_UART_Transmit_DMA(&huart5, (uint8_t *)data,length);
}


void LcdShow(void)
{
    char buff[256];
    LCD_SetAsciiFont(&ASCII_Font12);
    sprintf(buff,"numSV:%d  ",ubloxData.numSV);
    LCD_DisplayString(12,24,buff);
    sprintf(buff,"T:%d/%d/%d/%d/%d/%d    ",ubloxData.year,ubloxData.month,ubloxData.day,ubloxData.hour,ubloxData.min,ubloxData.sec);
    LCD_DisplayString(0,48,buff);
    sprintf(buff,"latitude:%f   ",ubloxData.latitude);
    LCD_DisplayString(0,72,buff);
    sprintf(buff,"longitude:%f   ",ubloxData.longitude);
    LCD_DisplayString(0,92,buff);
    sprintf(buff,"altitude:%f   ",ubloxData.altitude);
    LCD_DisplayString(0,112,buff);
    sprintf(buff,"Heading:%.2f gSpeed:%d   ",ubloxData.heading,ubloxData.speed);
    LCD_DisplayString(0,132,buff);
    sprintf(buff,"vN:%d vE:%d vD:%d   ",ubloxData.velN,ubloxData.velE,ubloxData.velD);
    LCD_DisplayString(0,152,buff);
    sprintf(buff,"hAcc:%.2f   ",ubloxData.hAcc);
    LCD_DisplayString(0,172,buff);
    sprintf(buff,"vAcc:%.2f  ",ubloxData.vAcc);
    LCD_DisplayString(0,192,buff);
    sprintf(buff,"s:%.2f   ",ubloxData.sAcc);
    LCD_DisplayString(0,212,buff);
    sprintf(buff,"c:%.2f  ",ubloxData.cAcc);
    LCD_DisplayString(0,232,buff);
    sprintf(buff,"time:%f  ",ubloxData.time);
    LCD_DisplayString(30,252,buff);
}

void GPS_Print(void)
{
    ring_printf("numSV:%d\r\n",ubloxData.numSV);
    ring_printf("numSV:%d\r\n",ubloxData.numSV);
    ring_printf("T:%d/%d/%d/%d/%d/%d\r\n",ubloxData.year,ubloxData.month,ubloxData.day,ubloxData.hour,ubloxData.min,ubloxData.sec);
    ring_printf("latitude:%f\r\n",ubloxData.latitude);
    ring_printf("longitude:%f\r\n",ubloxData.longitude);
    ring_printf("altitude:%f\r\n",ubloxData.altitude);
    ring_printf("Heading:%.2f gSpeed:%d\r\n",ubloxData.heading,ubloxData.speed);
    ring_printf("vN:%d vE:%d vD:%d\r\n",ubloxData.velN,ubloxData.velE,ubloxData.velD);
    ring_printf("hAcc:%.2f\r\n",ubloxData.hAcc);
    ring_printf("vAcc:%.2f\r\n",ubloxData.vAcc);
    ring_printf("s:%.2f\r\n",ubloxData.sAcc);
    ring_printf("c:%.2f\r\n",ubloxData.cAcc);
    ring_printf("time:%f\r\n",ubloxData.time);
}


/**
  * @brief UBX协议在数组中计算并设置校验和
  * @param buff 需要计算校验和的UBX数组
  * @param lenth 需要计算校验部分的UBX数组长度
  * @retval 校验和
  */
void UBX_SetCheckSum(uint8_t *ubxBuff,uint16_t lenth)
{
    UBX_CheckSum_t CkSum;
    int i;
    CkSum.A=0;
    CkSum.B=0;
    for(i=2; i<lenth-2; i++)
    {
        CkSum.A += ubxBuff[i];
        
        CkSum.B += CkSum.A;
    }
    ubxBuff[i++] = CkSum.A;
    ubxBuff[i] = CkSum.B;
}


/**
  * @brief UBX协议设置GPS波特率
  * @param None
  * @retval 校验和
  */
void UBX_SetBortRate(uint32_t bort)
{
    if(bort==9600||bort==38400||bort==115200)
    {
        memcpy(ubloxOutBuff,ubloxBaudOutConfig,sizeof(ubloxBaudOutConfig));
        
        /* 将设置的波特率写入发送数组，波特率在负载中偏移为8，长度为4，可查看数据手册 */
        memcpy(ubloxOutBuff+UBX_PYLOAD_OFFSET+8,(uint8_t *)&bort,4);
        /* 设置校验码 */
        UBX_SetCheckSum(ubloxOutBuff,sizeof(ubloxBaudOutConfig));
        
        ublox_SendData(ubloxOutBuff,sizeof(ubloxBaudOutConfig));
    }
}

/**
  * @brief UBX协议设置测量速率(单位ms)
  * @param None
  * @retval 校验和
  */
void UBX_SetMeasRate(uint16_t measRate)
{
    memcpy(ubloxOutBuff,ubloxRateOutConfig,sizeof(ubloxRateOutConfig));

    /* 将设置的波特率写入发送数组，波特率在负载中偏移为0，长度为2，可查看数据手册 */
    memcpy(ubloxOutBuff+UBX_PYLOAD_OFFSET+0,(uint8_t *)&measRate,2);
    /* 计算设置校验和 */ 
    UBX_SetCheckSum(ubloxOutBuff,sizeof(ubloxRateOutConfig));
    
    ublox_SendData(ubloxOutBuff,sizeof(ubloxRateOutConfig));
}

/**
  * @brief UBX协议复位GPS
  * @param None
  * @retval None
  */
void UBX_Reset(void)
{
    memcpy(ubloxOutBuff,ubloxResetConfig,sizeof(ubloxResetConfig));

    /* 计算设置校验和 */
    UBX_SetCheckSum(ubloxOutBuff,sizeof(ubloxResetConfig));
    
    ublox_SendData(ubloxOutBuff,sizeof(ubloxResetConfig));
}


/**
  * @brief ublox数据负载解析
  * @param None
  * @retval None
  */
static void ublox_PayloadDecode(ublox_PVT_t ublox_PVT)
{
	if(ublox.uclass == UBX_CLASS_NAV)
	{
		if(ublox.id == UBX_NAV_PVT )
		{
			ubloxData.time      = (float)ublox_PVT.iTOW / 1000;
			ubloxData.latitude  = (double)ublox_PVT.lat * (double)1e-7;
			ubloxData.longitude = (double)ublox_PVT.lon * (double)1e-7;
			ubloxData.altitude  = (float)ublox_PVT.hMSL * 0.001f;
			ubloxData.hAcc      = (float)ublox_PVT.hAcc * 0.001f;
			ubloxData.vAcc      = (float)ublox_PVT.vAcc * 0.001f;
			ubloxData.velN      = ublox_PVT.velN * 0.1f;
			ubloxData.velE      = ublox_PVT.velE * 0.1f;
			ubloxData.velD      = ublox_PVT.velD * 0.1f;
			ubloxData.speed     = ublox_PVT.gSpeed * 0.1f;
			ubloxData.heading   = ublox_PVT.heading * 1e-5f;
			ubloxData.sAcc      = ublox_PVT.sAcc * 0.001f;
			ubloxData.cAcc      = ublox_PVT.cAcc * 1e-5f;
			ubloxData.numSV     = ublox_PVT.numSV;
			ubloxData.fixStatus = ublox_PVT.gpsFix;
			ubloxData.year		= ublox_PVT.year;
			ubloxData.month		= ublox_PVT.month;
			ubloxData.day		= ublox_PVT.day;
			ubloxData.hour		= ublox_PVT.hour;
			ubloxData.min		= ublox_PVT.min;
			ubloxData.sec		= ublox_PVT.sec;
//            GPS_Print();
//            LcdShow();
		}
	}
}

/**
  * @brief ublox协议按字节解析
  * @param data 需要解析的字节数据
  * @retval None
  */
void ublox_DecodeByte(uint8_t data)
{
	/* 接收帧头1 */
	if(data == SYNC_CHAR1 && ublox.state == UBLOX_WAIT_SYNC1)
	{
		ublox.state = UBLOX_WAIT_SYNC2;
	}
	/* 接收帧头2 */
	else if(data == SYNC_CHAR2 && ublox.state == UBLOX_WAIT_SYNC2)
	{
		ublox.state = UBLOX_WAIT_CLASS;
	}
	/* 接收class */
	else if( ublox.state == UBLOX_WAIT_CLASS)
	{
		ublox.ckA = 0;
		ublox.ckB = 0;
		ublox.uclass = data;
		ublox.ckA += data;
		ublox.ckB += ublox.ckA;
		ublox.state = UBLOX_WAIT_ID;
	}
	/* 接收id */
	else if( ublox.state == UBLOX_WAIT_ID)
	{
		ublox.id = data;
		ublox.ckA += data;
		ublox.ckB += ublox.ckA;
		ublox.state = UBLOX_WAIT_LEN1;
	}
	/* 小端模式先来低位 */
	else if( ublox.state == UBLOX_WAIT_LEN1)
	{
		ublox.length = data;
		ublox.ckA += data;
		ublox.ckB += ublox.ckA;
		ublox.state = UBLOX_WAIT_LEN2;
	}
	/* 高位 */
	else if( ublox.state == UBLOX_WAIT_LEN2)
	{
		ublox.length = ublox.length|(data << 8);
		ublox.ckA += data;
		ublox.ckB += ublox.ckA;
		if(ublox.length>0&&ublox.length<255)
		{
			/* 开始接收数据 */
			ublox.state = UBLOX_PAYLOAD;
			ublox.count = 0;
		}
		else
		{
			/* 返回 */
			ublox.state = UBLOX_WAIT_SYNC1;
			ublox.length = 0;
		}
	}
	/* 消息负载 */
	else if( ublox.state == UBLOX_PAYLOAD)
	{
		ublox.ckA += data;
		ublox.ckB += ublox.ckA;
		ubloxRawData.PVT[ublox.count] = data;
		/* 接收完成 */
		if(++ublox.count == ublox.length)
		{
			ublox.state = UBLOX_CHECK1;
		}
	}
	/* CKA校验 */
	else if( ublox.state == UBLOX_CHECK1)
	{
		if( data == ublox.ckA)
		{
			ublox.state = UBLOX_CHECK2;
		}
	}
	/* CKB校验 */
	else if( ublox.state == UBLOX_CHECK2)
	{
		ublox.state = UBLOX_WAIT_SYNC1;
		if( data == ublox.ckB)
		{
			ublox_PayloadDecode(ubloxRawData.ublox_PVT);
		}
		else
		{
            
		}
	}
}


/**
  * @brief GPS以UBX协议解析数据
  * @param buff 需要解析的UBX数组
  * @param lenth 需要解析的UBX数组长度
  * @param lenth 需要解析的UBX数组长度
  * @retval 解析完成后的GPS数据
  */
ubloxData_t GPS_UBX_Decode(uint8_t *buff,uint16_t lenth)
{
    for(int i=0;i<lenth;i++)
    {
        ublox_DecodeByte(buff[i]);
    }
    return ubloxData;
}



/**
  * @brief UBX协议设置消息速率(单位S),即测量几次输出一次数据
  *        如采用UBX_SetMeasRate(100)函数将GPS测量速率设置为10HZ,rate为2时数据输出速率为5HZ，1时为10HZ
  *        Set/Get message rate configuration (s) to/from the receiver.
  *        Send rate is relative to the event a message is registered on. For example, if the rate of a
  *        navigation message is set to 2, the message is sent every second navigation solution. 
  * @param uclass Message Class
  * @param uclass Message Identifier
  * @param rate Send rate on I/O Port (6 Ports)
  * @retval None
  */
static void UBX_SetMessageRate(uint8_t uclass, uint8_t id, uint8_t rate)
{
    uint8_t dataCnt = 0;
    uint8_t ck_a = 0, ck_b = 0;

    ubloxOutBuff[dataCnt++] = SYNC_CHAR1;
    ubloxOutBuff[dataCnt++] = SYNC_CHAR2;
    ubloxOutBuff[dataCnt++] = UBX_CLASS_CFG;
    ubloxOutBuff[dataCnt++] = UBX_CFG_MSG;   
    ubloxOutBuff[dataCnt++] = 0x03;            
    ubloxOutBuff[dataCnt++] = 0x00;            

    ubloxOutBuff[dataCnt++] = uclass;           
    ubloxOutBuff[dataCnt++] = id;              
    ubloxOutBuff[dataCnt++] = rate;            

    for(uint8_t i=2; i<dataCnt; i++)
    {
        ck_a += ubloxOutBuff[i];
        ck_b += ck_a;
    }

    ubloxOutBuff[dataCnt++] = ck_a;
    ubloxOutBuff[dataCnt++] = ck_b;

    ublox_SendData(ubloxOutBuff, dataCnt);
}

/**
  * @brief 获取gps原始数据
  * @param None
  * @retval None
  */
ubloxData_t GPS_GetData(void)
{
	return ubloxData;
}
