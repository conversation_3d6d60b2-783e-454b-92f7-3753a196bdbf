/*! ----------------------------------------------------------------------------
 * @file    bsp_uart.c
 * 
 * @version V1.00 
 *
 * @brief   板级串口驱动源文件，串口采用空闲中断的方式判断接收数据长度。
 *          UART1采用DMA方式收发数据。
 *        
 *          使用步骤：
 *          1.将HAL_UARTEx_ReceiveToIdle_DMA(&huart1, Uart1RxBuffer, USART1_RxBuffer_Size);放入usart.c文件中MX_USART1_UART_Init(void)函数最后一行。
 *          2.采用Uart1RingBufferInit()函数对串口1RingBuffer初始化
 *          3.在HAL_UARTEx_RxEventCallback()回调函数中执行数据处理逻辑
 *          4.在任务循环中使用CheckUart1Update()获取串口1Ringbuffer接收数据
 *          
 *          使用例程UART1（在循环中执行）
 *          实现将串口1接收的数据发送返回
 *          uint8_t readBuff[256]={0};
 *          uint16_t uart1RevLen;
 *          while(1)
 *          {
 *             
 *              if(UART1_CheckUpdate())
 *              {
 *                 HAL_UART_Transmit(&huart1, (uint8_t *)readBuff,uart1RevLen);
 *              }
 *          }
 *          注意：readBuff在任务中定义的时候一定要注意任务最大可分配数组大小，如果任务内存溢出会发生问题！
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/09/02   DSS       首版
 *
 */
#include "bsp_uart.h"
#include "usart.h"
#include "bsp_print.h"
#include "dma.h"
#include "string.h"
#include "bsp_ringBuffer.h"
#include "bsp_gps_m8n.h"
#include "bsp_rc.h"
#include "RobotMove.h"
#include "robot.h"
#include "sensor.h"
#include "bsp_ws2812.h"
#include "bsp_beep.h"

#include "robot.h"
#include "sensor.h"
#include "bsp_beep.h"
#include "bsp_ws2812.h"
#include "bsp_tim_dma.h"

#if (USE_LETTER_SHELL)
#include "shell.h"
#include "shell_port.h"
#include "bsp.h"
#endif


/* 串口接收数组 */
uint8_t Uart1RxBuffer[USART1_RxBuffer_Size];
uint8_t Uart1TxBuffer[USART1_TxBuffer_Size];

uint8_t Uart4RxBuffer[USART4_RxBuffer_Size];
uint8_t Uart4TxBuffer[USART4_TxBuffer_Size];

uint8_t Uart5RxBuffer[USART5_RxBuffer_Size];
uint8_t Uart5TxBuffer[USART5_TxBuffer_Size];

uint8_t uart8RxBuffer[USART8_RxBuffer_Size];
/* 外部变量 */
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart1_tx;

/* 定义uart1的RingBuffer */
ringbuffer_t uart1RxRingBuffer;
ringbuffer_t uart1TxRingBuffer;

/* 定义uart4的RingBuffer */
ringbuffer_t uart4RxRingBuffer;
ringbuffer_t uart4TxRingBuffer;

/* 定义uart1的RingBuffer数组 */
uint8_t Uart1RxRingBuffer[RING_BUFFER_SIZE];
uint8_t Uart1TxRingBuffer[RING_BUFFER_SIZE];

/* 定义uart4的RingBuffer数组 */
uint8_t Uart4RxRingBuffer[RING_BUFFER_SIZE];
uint8_t Uart4TxRingBuffer[RING_BUFFER_SIZE];

/**
  * @brief  串口空闲中断回调函数
  * @param  *huart 串口句柄
  * @retval Size 接收数组大小
  */
void Uart1RingBufferInit(void)
{
    ringbuffer_init(&uart1TxRingBuffer, Uart1TxRingBuffer, RING_BUFFER_SIZE);
    ringbuffer_init(&uart1RxRingBuffer, Uart1RxRingBuffer, RING_BUFFER_SIZE);
    ringbuffer_init(&uart4TxRingBuffer, Uart4TxRingBuffer, RING_BUFFER_SIZE);
    ringbuffer_init(&uart4RxRingBuffer, Uart4RxRingBuffer, RING_BUFFER_SIZE);
}


uint8_t readBufferRX[256]={0};

/**
  * @brief  串口空闲中断回调函数
  * @param  *huart 串口句柄
  * @retval Size 接收数组大小
  */
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	if(huart->Instance==USART1)
	{
        /*  收到心跳包数据 */
        if(Uart1RxBuffer[0] == 0x01)
        {
            /* 如果未建立通信 */
            if(Robot_GetStatus().Commut_Status == NO_COMMUT)
            {
                /* 让蜂鸣器长响1S */
                BeepStart(100, 5, 1);       
                /* 收到心跳包数据后让上灯带全部显示黑色 */
                WS2812ShowSameColor(WS_BLACK,WS2812_UP); 
            }
            FeedDog_Heart();
            /* 设置ID */
            SetRobotID(Uart1RxBuffer[1]); 
            /* 设置摄像头状态 */
            if(Uart1RxBuffer[2] == 0)
            {
               SensorStatus.CAM_Status = SENSOR_ERROR;
            }
            else if(Uart1RxBuffer[2] == 1)
            {
                SensorStatus.CAM_Status = SENSOR_OK;
            }
        }
        /* 上灯带控制 */
        else if(Uart1RxBuffer[0] == 0x02)
        {
            uint8_t pixNum;
            uint32_t color = 0;
            uint8_t pixCount =0;
            uint8_t pixNumAll = 0; /* 控制的总灯珠数量 */

            /* 清除显示数组 */
            WS2812_ClearShowBuffer(WS2812_UP);
            
            FeedDog_RGB();
            for(int i = 0;i < (Size-1)/4; i++)
            {
                pixNum = Uart1RxBuffer[i*4+1 + 3];
                pixNumAll += pixNum;
                
                /* 判断灯珠数量 */
                if(pixNumAll > PIX_NUM_UP)
                {
                    /* 如果数量超了，则超的部分不显示 */
                    if(pixNumAll - pixNum > PIX_NUM_UP)
                    {
                        pixNum = 0;
                    }
                    else 
                    {
                        pixNum = PIX_NUM_UP - (pixNumAll - pixNum);
                    }
                    
                }
                
                memcpy(&color,Uart1RxBuffer + i*4+1,3);
                
                for(int j = 0; j < pixNum; j++)
                {
                    WS2812ShowBuffer_UP[pixCount] = color;
                    pixCount++;
                }
            }      
        }
        /* 下灯带控制 */
        else if(Uart1RxBuffer[0] == 0x03)
        {
            uint8_t pixNum;
            uint32_t color = 0;
            uint8_t pixCount =0;
            uint8_t pixNumAll = 0; /* 控制的总灯珠数量 */
            
            /* 如果电量足够接收下灯带控制指令，电量不足则不接受下灯带控制指令 */
            if(Sensor_GetStatus().Battery_Status == BAT_ENOUGH_POWER)
            {
                FeedDog_RGB();
                /* 清除显示数组 */
                WS2812_ClearShowBuffer(WS2812_DOWN);
                for(int i = 0;i<(Size-1)/4;i++)
                {
                    pixNum = Uart1RxBuffer[i*4+1 + 3];
                    pixNumAll += pixNum;
                    
                     /* 判断灯珠数量 */
                    if(pixNumAll > PIX_NUM_DOWN)
                    {
                        /* 如果数量超了，则超的部分不显示 */
                        if(pixNumAll - pixNum > PIX_NUM_DOWN)
                        {
                            pixNum = 0;
                        }
                        else 
                        {
                            pixNum = PIX_NUM_DOWN - (pixNumAll - pixNum);
                        }
                    }
                    memcpy(&color,Uart1RxBuffer + i*4+1,3);
                    
                    for(int j = 0; j<pixNum; j++)
                    {
                        WS2812ShowBuffer_DOWN[pixCount] = color;

                        pixCount++;
                    }
                }
            }
        }
        
#if (USE_LETTER_SHELL)
        if(ringbuffer_isEmpty(&uart1RxRingBuffer)==0)
        {
            uint16_t len = ringbuffer_getUsedSize(&uart1RxRingBuffer);
            
            ringbuffer_out(&uart1RxRingBuffer, readBufferRX, len);

            for(uint8_t i = 0; i < len; i++)
            {
                shellHandler(&shell, readBufferRX[i]);	
            }
        }  
#endif
        /* 测试函数，把接收到的数据全部发出 */
//        HAL_UART_Transmit_DMA(&UART_DEBUG_Handle, (uint8_t *)Uart1RxBuffer,Size);   
        /* 重新设置串口接收空闲中断，采用DMA方式 */
		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, Uart1RxBuffer, USART1_RxBuffer_Size);	
	}
    else if(huart->Instance==UART4)
    {   
        if(Uart4RxBuffer[0] == 0x02)
        {
            RobotSetControlMode(MOVE_CONTROL);
            RobotMove(0.1,0,0);
        }
        else if(Uart4RxBuffer[0] == 0x03)
        {
            RobotSetControlMode(MOVE_CONTROL);
            RobotMove(0,0.1,0);
        }
        else if(Uart4RxBuffer[0] == 0x04)
        {
            RobotSetControlMode(MOVE_CONTROL);
            RobotMove(0,0,0);
        }
        HAL_UARTEx_ReceiveToIdle_DMA(&huart4, Uart4RxBuffer, USART4_RxBuffer_Size);
    }
    else if(huart->Instance == UART5)
    {
        SensorStatus.GPS_Status = SENSOR_ERROR;
        GPS_UBX_Decode(Uart5RxBuffer,Size);
        HAL_UARTEx_ReceiveToIdle_DMA(&huart5, Uart5RxBuffer, USART5_RxBuffer_Size);
    }
}
 
/**
  * @brief  串口接收回调函数
  * @param  huart 串口句柄
  * @retval 有数据更新返回1，否则返回0
  */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART8)
    {
        SBUS_Decode(uart8RxBuffer[0]);
        HAL_UART_Receive_IT(&huart8, &uart8RxBuffer[0], 1);
    }
}

/**
  * @brief  向UART1的TxRingBuffer写入数据
  * @param  *data 需要写入的数据
  *         Size 需要写入的数据大小
  * @retval None
  */
void UART1_RingBufferWrite(uint8_t *data, uint16_t Size)
{
    ringbuffer_in(&uart1TxRingBuffer, data, Size);      
}

/**
  * @brief  向UART4的TxRingBuffer写入数据
  * @param  *data 需要写入的数据
  *         Size 需要写入的数据大小
  * @retval None
  */
void UART4_RingBufferWrite(uint8_t *data, uint16_t Size)
{
    ringbuffer_in(&uart4TxRingBuffer, data, Size);      
}

/**
  * @brief  检测是否UART1的RingBuffer中有数据，有数据则发送，使用时需放入周期调用函数中，如bsp_RunPer10ms()
  * @param  None
  * @retval None
  */
void UART1_RingBufferCheckSend(void)
{
    uint16_t usedSize = ringbuffer_getUsedSize(&uart1TxRingBuffer);
    /* 如果有数据未发送 */
    if(usedSize>0)
    {
        ringbuffer_out(&uart1TxRingBuffer, Uart1TxBuffer, usedSize);
        HAL_UART_Transmit_DMA(&huart1, (uint8_t *)Uart1TxBuffer,usedSize);
    }
}

/**
  * @brief  检测是否UART4的RingBuffer中有数据，有数据则发送，使用时需放入周期调用函数中，如bsp_RunPer10ms()
  * @param  None
  * @retval None
  */
void UART4_RingBufferCheckSend(void)
{
    uint16_t usedSize = ringbuffer_getUsedSize(&uart4TxRingBuffer);
    /* 如果有数据未发送 */
    if(usedSize>0)
    { 
//        if(usedSize>255)  usedSize = 255;
        ringbuffer_out(&uart4TxRingBuffer, Uart4TxBuffer, usedSize);
        HAL_UART_Transmit_DMA(&huart4, (uint8_t *)Uart4TxBuffer,usedSize);
    }
}

/**
  * @brief  检测串口1 RingBuffer是否有数据更新，放入任务循环
  * @param[out]  *readData:读取的数据指针
  * @param[out]  *len:读取的数据长度指针
  * @retval 有数据更新返回1，否则返回0
  */
uint8_t UART1_CheckUpdate(uint8_t *readData,uint16_t *len)
{
    if(ringbuffer_isEmpty(&uart1RxRingBuffer)==0)
    {
        uint16_t size = ringbuffer_getUsedSize(&uart1RxRingBuffer);
        ringbuffer_out(&uart1RxRingBuffer, readData, size);
        *len = size;
        
        return 1;
    }
    return 0;
}

/**
  * @brief  检测串口4 RingBuffer是否有数据更新，放入任务循环
  * @param[out]  *readData:读取的数据指针
  * @param[out]  *len:读取的数据长度指针
  * @retval 有数据更新返回1，否则返回0
  */
uint8_t UART4_CheckUpdate(uint8_t *readData,uint16_t *len)
{
    if(ringbuffer_isEmpty(&uart4RxRingBuffer)==0)
    {
        uint16_t size = ringbuffer_getUsedSize(&uart4RxRingBuffer);
        ringbuffer_out(&uart4RxRingBuffer, readData, size);
        *len = size;
        
        return 1;
    }
    return 0;
}


