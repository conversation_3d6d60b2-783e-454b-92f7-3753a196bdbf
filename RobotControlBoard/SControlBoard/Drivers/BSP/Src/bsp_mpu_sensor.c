/*! ----------------------------------------------------------------------------
 * @file    bsp_mpu_sensor.c
 * 
 * @version V1.00 
 *
 * @brief   此文件为MPU传感器硬件驱动板级支持文件，采用SPI通信，中断触发
 *          支持MPU6500和MPU9250传感器
 *
 *          HAL库参数配置
 *          1.SPI通信时钟为1MHZ,SPI_POLARITY_HIGH,SPI_PHASE_2EDGE,SPI_CRCCALCULATION_DISABLE,CS片选信号低电平有效
 *          2.SPI_DATASIZE_8BIT
 *          3.MPU中断引脚配置为外部中断低电平触发
 *
 *          使用步骤：
 *          1.调用MPU_SensorInit()初始化函数
 *          2.将MPU_Sensor_EXTI_CallBack(uint16_t GPIO_Pin)函数放入外部引脚触发中断 HAL_GPIO_EXTI_Callback()
 *          3.通过MPU_SensorCheckUpdata()函数判断是否有数据更新
 *          4.如有数据更新则调用mpu_dmp_get_data(&pitch,&roll,&yaw)函数更新数据
 *          5.在MotionDriver文件夹的inv_mpu.h文件中设置DEFAULT_MPU_HZ来改变数据输出频率
 *
 *          使用例程（在循环中执行）
 *          if(MPU_SensorCheckUpdata())
 *          {
 *              mpu_dmp_get_data(&pitch,&roll,&yaw);
 *              printf("P:%.2f  R:%.2f  Y:%.2f\r\n",pitch,roll,yaw);
 *          }
 *
 *          注意事项：
 *          1.当使用MPU外部中断时，需将MPU_Sensor_EXTI_CallBack()函数放入HAL库HAL_GPIO_EXTI_Callback()函数，
 *            并通过MPU_SensorCheckUpdata()函数判断数据是否更新。
 *          2.关于官方Motion Driver驱动在inv_mpu.h文件中配置。
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/24   DSS       首版
 *
 * <AUTHOR>
 */
#include "bsp_mpu_sensor.h"
#include "spi.h"
#include "bsp_print.h"
#include "bsp_ak89xx.h"

/* 定义MPU传感器数据更新标志位 在中断中置位*/
static uint8_t MPU_SensorUpdate = 0;


/**
  * @brief   MPU运动传感器写字节
  * @param   reg:寄存器地址
  * @param	 data：需要写入的字节数据
  * @retval  
  */
void MPU_WriteByte(uint8_t reg, uint8_t data)
{
    MPU_CS(0);
    HAL_SPI_Transmit(&MPU_SPI_HANDLE,(uint8_t *)&reg,1,0xff);
    HAL_SPI_Transmit(&MPU_SPI_HANDLE,&data,1,0xff);
    MPU_CS(1);
}

/**
  * @brief   MPU运动传感器读取字节
  * @param   reg:寄存器地址
  * @retval  读到的数据
  */
uint8_t MPU_ReadByte(uint8_t reg)
{
	uint8_t rxData;
    MPU_CS(0);
    /* 将MSB设置为1表示读，将MSB设置为0表示写 */
    reg = reg|0x80;
    HAL_SPI_Transmit(&MPU_SPI_HANDLE,(uint8_t *)&reg,1,0xff);
    HAL_SPI_Receive(&MPU_SPI_HANDLE,&rxData,1,0xff);
    MPU_CS(1);
    return rxData;
}

/**
  * @brief   MPU运动传感器写设置长度字节
  * @param   reg:寄存器地址
  * @param	 *data：存储数据的缓冲区
  * @param	 len：要读取的数据长度
  * @retval  
  */
void MPU_WriteLen(uint8_t reg, uint8_t *data,uint8_t len)
{
    MPU_CS(0);
    HAL_SPI_Transmit(&MPU_SPI_HANDLE,(uint8_t *)&reg,1,0xff);
    HAL_SPI_Transmit(&MPU_SPI_HANDLE,data,len,0xff);
    MPU_CS(1);
}


/**
  * @brief   从MPU运动传感器寄存器读取设置长度字节
  * @param   reg:寄存器地址
  * @param	 *data：存储数据的缓冲区
  * @param	 len：要读取的数据长度
  * @retval  
  */
void MPU_ReadLen(uint8_t reg,uint8_t *data,uint8_t len)
{
    MPU_CS(0);
    /* 将MSB设置为1表示读，将MSB设置为0表示写 */
    reg = reg|0x80;
    HAL_SPI_Transmit(&MPU_SPI_HANDLE,(uint8_t *)&reg,1,0xff);;
    HAL_SPI_Receive(&MPU_SPI_HANDLE,data,len,0xff);
    MPU_CS(1);
}


/**
  * @brief   设置MPU运动传感器陀螺仪传感器满量程范围
  * @param   fsr: 0,±250dps;1,±500dps;2,±1000dps;3,±2000dps
  * @retval  None
  */
void MPU_SetGyroFsr(uint8_t fsr)
{
    /* 设置陀螺仪满量程范围 */
    MPU_WriteByte(MPU_RA_GYRO_CONFIG, (fsr<<3)|3); 
}


/**
  * @brief   设置MPU运动传感器加速度传感器满量程范围
  * @param   fsr:0,±2g;1,±4g;2,±8g;3,±16g
  * @retval  None
  */
void MPU_SetAccelFsr(uint8_t fsr)
{
    /* 设置加速度传感器满量程范围  */
	MPU_WriteByte(MPU_RA_ACCEL_CONFIG,fsr<<3);
}

/**
  * @brief   设置MPU运动传感器的数字低通滤波器
  * @param   lpf:数字低通滤波频率(Hz)
  * @retval  None
  */
void MPU_Set_LPF(uint16_t lpf)
{
	uint8_t data=0;
	if(lpf>=188)data=1;
	else if(lpf>=98)data=2;
	else if(lpf>=42)data=3;
	else if(lpf>=20)data=4;
	else if(lpf>=10)data=5;
	else data=6; 
    /* 设置数字低通滤波器 */ 
	MPU_WriteByte(MPU_RA_CONFIG,data);
}


/**
  * @brief   设置MPU运动传感器的采样率(假定Fs=1KHz)
  * @param   4~1000(Hz)
  * @retval  None
  */
void MPU_SetRate(uint16_t rate)
{
	uint8_t data;
	if(rate>1000)rate=1000;
	if(rate<4)rate=4;
	data=1000/rate-1;
    /* 设置采样频率 */
	MPU_WriteByte(MPU_RA_SMPLRT_DIV,data);	
    /* 设置数字低通滤波器LPF为采样率的一半 */
 	MPU_Set_LPF(rate/2);	
}


/**
  * @brief   得到温度值
  * @param   None
  * @retval  温度值(扩大了100倍)
  */
short MPU_GetTemperature(void)
{
    uint8_t buf[2]; 
    short raw;
	float temp;
	MPU_ReadLen(MPU_RA_TEMP_OUT_H,buf,2); 
    raw=((uint16_t)buf[0]<<8)|buf[1];  
    temp=21+((double)raw)/333.87;  
    return temp*100;;
}

/**
  * @brief   得到陀螺仪值(原始值)
  * @param   gx,gy,gz:陀螺仪x,y,z轴的原始读数(带符号)
  * @retval  None
  */
void MPU_GetGyroscope(short *gx,short *gy,short *gz)
{
    uint8_t buf[6];
    
	MPU_ReadLen(MPU_RA_GYRO_XOUT_H,buf,6);

    *gx=((uint16_t)buf[0]<<8)|buf[1];  
    *gy=((uint16_t)buf[2]<<8)|buf[3];  
    *gz=((uint16_t)buf[4]<<8)|buf[5];
}

/**
  * @brief   得到加速度值(原始值)
  * @param   ax,ay,az:加速度计x,y,z轴的原始读数(带符号)
  * @retval  None
  */
void MPU_GetAccelerometer(short *ax,short *ay,short *az)
{
    uint8_t buf[6];  
    
    MPU_ReadLen(MPU_RA_ACCEL_XOUT_H,buf,6);

    *ax=((uint16_t)buf[0]<<8)|buf[1];  
    *ay=((uint16_t)buf[2]<<8)|buf[3];  
    *az=((uint16_t)buf[4]<<8)|buf[5];
}



/**
  * @brief  MPU运动传感器传感器初始化函数
  * @param  None
  * @retval 0:成功 其他：失败
  */
uint8_t MPU_SensorInit(void)
{
    uint8_t data=0;
    /* 拉高磁力传感器CS引脚防止数据读错 */
    MAG_CS(1);
    MPU_DELAY(10);
    
    MPU_WriteByte(MPU_RA_PWR_MGMT_1,0X80);     /* 复位MPU6500 */
    MPU_DELAY(100);                            /* 延时100ms */
    MPU_WriteByte(MPU_RA_PWR_MGMT_1,0X00);     /* 唤醒MPU6500 */
    
    MPU_SetGyroFsr(3);					       /* 陀螺仪传感器,±2000dps */
    MPU_SetAccelFsr(0);					       /* 加速度传感器,±2g */
    MPU_SetRate(100);						   /* 设置采样率50Hz */
    
    MPU_WriteByte(MPU_RA_INT_ENABLE,0X00);     /* 关闭所有中断 */
    MPU_WriteByte(MPU_RA_USER_CTRL,0X00);      /* I2C主模式关闭 */
    MPU_WriteByte(MPU_RA_FIFO_EN,0X00);	       /* 关闭FIFO */
    MPU_WriteByte(MPU_RA_INT_PIN_CFG,0X82);    /* INT引脚低电平有效，开启bypass模式，可以直接读取磁力计 */
    
    data = MPU_ReadByte(MPU_RA_WHO_AM_I);      /* 读取MPU的ID */
    
    ring_printf("data:%x\r\n",data);

    if(data==MPU_SPI_ID) 				       /* 是否器件ID正确 */
    {
        MPU_WriteByte(MPU_RA_PWR_MGMT_1,0X01); /* 设置CLKSEL,PLL X轴为参考 */
        MPU_WriteByte(MPU_RA_PWR_MGMT_2,0X00); /* 加速度与陀螺仪都工作 */
        MPU_SetRate(100);					   /* 设置采样率为50Hz */
    }
    else return 1;
    
    return 0;
}

/**
  * @brief  检查是否MPU传感器数据更新
  *         使用示例，放入循环：
  *         if(MPU_SensorCheckUpdata())
  *         {
  *             //取出数据做进一步处理
  *             mpu_mpl_get_data(&pitch,&row,&yall);
  *         }
  * @param  None
  * @retval 如果数据更新返回1，否则返回0
  */
uint8_t MPU_SensorCheckUpdata(void)
{
    if(MPU_SensorUpdate == 1)
    {     
        MPU_SensorUpdate=0;
        return 1;
    }
    else return 0;
}

#if MPU_USE_INT 

/**
  * @brief  MPU传感器外部中断回调函数
  * @param  GPIO_Pin：中断引脚
  * @retval None
  */
void MPU_Sensor_EXTI_CallBack(uint16_t GPIO_Pin)
{
    if(GPIO_Pin == MPU_INT_Pin)
    {     
        MPU_SensorUpdate = 1;
    }
}

#endif 


/* 以下函数用于官方MPL处理库函数实现 */

/**
  * @brief  重定义MPU写函数用于官方DMP库
  * @param  None
  * @retval None
  */
int MPU_Sensor_SPI_Write(uint8_t sel, uint8_t reg_addr, uint8_t length, uint8_t const *data)
{
    /* MPU IIC地址 */
    if(sel == MPU_IIC_ADRESS)      
    {
        MAG_CS(1);
        MPU_WriteLen(reg_addr,(uint8_t *)data,length);
    }
    /* 磁力计IIC地址 */
    else if(sel == MAG_IIC_ADRESS) 
    {
        MPU_CS(1);
        MAG_WriteLen(reg_addr,(uint8_t *)data,length);
    }
    return 0;
}

/**
  * @brief  重定义MPU读函数用于官方DMP库
  * @param  None
  * @retval None
  */
int MPU_Sensor_SPI_Read(uint8_t sel, uint8_t reg_addr,uint8_t length, uint8_t *data) 
{
    if(sel == 0x68)      /* MPU地址 */
    {
        MAG_CS(1);
        MPU_ReadLen(reg_addr,data,length);
    }
    else if(sel == 0x0c) /* 磁力计地址 */
    {
        MPU_CS(1);
        MAG_ReadLen(reg_addr,(uint8_t *)data,length);
    }
    return 0;
}

/**
    * @brief  重定义读时间函数用于官方DMP库
  * @param  None
  * @retval None
  */
void MPU_Get_MS(unsigned long *time)
{
    *time=(unsigned long)HAL_GetTick();
}


