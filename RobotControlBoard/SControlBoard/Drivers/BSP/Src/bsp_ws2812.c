/*! ----------------------------------------------------------------------------
 * @file    bsp_ws2812.c
 * 
 * @version V1.00 
 *
 * @brief   此程序为WS2812 RGB彩色灯珠的驱动程序，驱动方案为定时器PWM加上DMA输出信号以节省CPU的资源。
 *          WS2812 24位颜色从高到低位为（8位G+8位R+8位B），需要800KHz的PWM频率驱动，数据1：T1L 0.4us 
 *          T1H 0.8us   数据0：T0L 0.8us T0L 0.4us ,采用CubeMX设置。
 *
 *          CubeMX参数设置
 *          1.GPIO设置：GPIO.Pull需设置为GPIO_PULLUP和GPIO.Speed需设置为GPIO_SPEED_FREQ_VERY_HIGH
 *          2.定时器：定时器设置为PWM输出，频率800KHZ  / PWM_MODE1 / TIM_OCPOLARITY_HIGH / TIM_AUTORELOAD_PRELOAD_ENABLE
 *          3.DMA 设置为Memory到Peripheral  设备和内存数据宽度设置为为Half World 不使用FIFO
 *        
 *          使用步骤：
 *          1.在bsp_ws2812.h中定义灯组数量和使用的定时器
 *          2.设置WS2812ShowBuffer[]颜色数组，WS2812ShowBuffer[0]代表第一个灯珠的颜色，以此类推，灯组24位颜色从高到低位为（8位G+8位R+8位B）。
 *          3.调用WS2812ShowColor()函数显示设置的数组颜色
 *          
 *          例子：
 *          memset(WS2812ShowBuffer,0,PIX_NUM); //清零显示数组
 *          WS2812ShowBuffer[0] = WS_RED;       //设置数组第一个显示颜色为红色
 *          WS2812ShowColor();                  //显示数组颜色
 *          //每隔500ms让灯珠颜色循环左移
 *          while(1)
 *          {
 *              WS2812ShowColorCircle(LEFT);    
 *              HAL_Delay(500);      
 *          }
 *
 *          注意：
 *          ①使用的定时器一定要带DMA!!!
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS       采用定时器DMA方式
 *      V2.00   2022/06/12   DSS       采用DMA控制引脚输出电平方式
 *
 */
 
#include "bsp_ws2812.h"
#include "usart.h"
#include "bsp_print.h"
#include "tim.h"
#include "rng.h"
#include "sensor.h"
#include "bsp_tim_dma.h"
#include "robot.h"

/* T0H 0.4us T1H 0.85us     T0L 0.85us  T1L 0.4us */

/* 数据位低电平时间 */
#define WS_BIT_LOW_TIME (int)(0.32*WS2812_PWM_TIMER_PERIOD)
/* 数据位高电平时间 */
#define WS_BIT_HIGH_TIME (int)(0.68*WS2812_PWM_TIMER_PERIOD)


/* 这里需要特别注意！！！！ */

#if USE_TIMER_PWM_DMA
/* STM32H7 DMA1和DMA2不能访问DTCM段地址为0x20000000，但可以AXI SRAM区域，故可以把内存地址设为0x24000000 */
static uint16_t wstx[24*PIX_NUM] __attribute__((section(".ARM.__at_0x24000000")));
#endif

#if USE_GIPO_TIMER_DMA
/* 采用GPIO DMA方式定义的数组 */
static ALIGN_32BYTES(__attribute__((section (".RAM_D3"))) uint32_t wsgpio1[72*(PIX_NUM_DOWN+1)]);
static ALIGN_32BYTES(__attribute__((section (".RAM_D3"))) uint32_t wsgpio2[72*(PIX_NUM_UP+1)]);

#endif

/* 定义WS2812RGB彩灯显示数组 */
uint32_t WS2812ShowBuffer_UP[PIX_NUM_UP];
uint32_t WS2812ShowBuffer_DOWN[PIX_NUM_DOWN];

/* 定义WS2812模式变量 */
static uint8_t WS2812_MODE = BREATH_MODE;


/**
  * @brief  不使能彩灯在定时器中控制
  * @param  None
  * @retval None
  */
void RGB_TimerControlDisable(void)
{
    /* 先暂停DMA传输否则会出现数据冲突 */
    HAL_DMA_Abort(&WS2812_GPIO_DMA_Handle);
}

/**
  * @brief  清空WS2812ShowBuffer数组
  * @param  None
  * @retval None
  */
void WS2812_ClearShowBuffer(WS2812_POS_e pos)
{
    if(pos == WS2812_UP)
    {
        for (int i=0;i<PIX_NUM_UP;i++)
        {
            WS2812ShowBuffer_UP[i] = WS_BLACK;
        }
    }
    else if(pos == WS2812_DOWN)
    {
        for (int i=0;i<PIX_NUM_DOWN;i++)
        {
            WS2812ShowBuffer_DOWN[i] = WS_BLACK;
        }
    } 
}

/**
  * @brief  WS2812采用GPIO DMA方式初始化函数
  * @param  color 需要显示的颜色，低24位有用
  *         24位颜色从高到低位为（8位G+8位R+8位B）
  *         如全部显示红色则color为0x00ff00
  * @retval None
  */
void WS2812_GPIO_TIMRE_DMA_Init(void)
{
    bsp_InitTimDMA();
    WS2812_ClearShowBuffer(WS2812_UP);
    WS2812_ClearShowBuffer(WS2812_DOWN);
}

/**
  * @brief  WS2812全部显示统一的颜色
  * @param[in]  color 需要显示的颜色，低24位有用
  *             24位颜色从高到低位为（8位G+8位R+8位B）
  *             如全部显示红色则color为0x00ff00
  * @param[in]  pos 彩灯位置
  * @retval None
  */
void WS2812ShowSameColor(uint32_t color,WS2812_POS_e pos)
{
    if(pos == WS2812_UP)
    {
        for (int i=0;i<PIX_NUM_UP;i++)
        {
            WS2812ShowBuffer_UP[i] = color;
        }
    }
    else if(pos == WS2812_DOWN)
    {
        for (int i=0;i<PIX_NUM_DOWN;i++)
        {
            WS2812ShowBuffer_DOWN[i] = color;
        }
    }
}

/**
  * @brief  WS2812随机显示颜色
  * @param  None
  * @retval None
  */
void WS2812ShowRadomColor(WS2812_POS_e pos)
{
    if(pos == WS2812_UP)
    {
        WS2812FillColorBuffRadom(WS2812ShowBuffer_UP);
    }
    else if(pos == WS2812_DOWN)
    {
        WS2812FillColorBuffRadom(WS2812ShowBuffer_DOWN);
    }
}

/**
  * @brief  WS2812循环移动颜色，每调用一次移动一次
  * @param  dir：移动的方向，LEFT 左移，RIGHT 右移   
  * @retval None
  */
void WS2812ShowColorCircle(uint8_t dir,WS2812_POS_e pos)
{
    if(pos == WS2812_UP)
    {
        MoveColorBuff(WS2812ShowBuffer_UP,dir);
    }
    else if(pos == WS2812_DOWN)
    {
        MoveColorBuff(WS2812ShowBuffer_DOWN,dir);
    }
}

/**
  * @brief  WS2812将随机生成颜色数组填充到ColorBuff
  * @param  *color 颜色数组指针
  * @retval None
  */
void WS2812FillColorBuffRadom(uint32_t *color)
{
    uint32_t radomData;
    
    for (int i=0; i<PIX_NUM_DOWN ;i++)
    {
        HAL_RNG_GenerateRandomNumber(&hrng,&radomData);
        *color = radomData;
        color++;
    }
}

#if USE_TIMER_PWM_DMA 
/**
  * @brief  WS2812加载24个位函数,控制ws2812颜色
  * @param  *color 需要显示的颜色数组	
  * @retval None
  */
void WS2812LoadColor(uint32_t *color)
{
	uint16_t i=0,j=0;
	uint16_t lenth=PIX_NUM;
	uint32_t ctemp=0;
	for(i=0;i<lenth;i++)
	{
		ctemp=*(color+i);
		for(j=0;j<24;j++)
		{		
			if(ctemp&0x800000)
			{
				wstx[j+24*i]=WS_BIT_HIGH_TIME;
			}
			else
			{
				wstx[j+24*i]=WS_BIT_LOW_TIME;
			}
			ctemp<<=1;
		}	
	}
	*(color+PIX_NUM)=0;
}
#endif

#if USE_GIPO_TIMER_DMA
/**
  * @brief  WS2812加载24个位函数,控制ws2812颜色
  * @param  *color 需要显示的颜色数组	
  * @param  pos 需要加载颜色的通道，0，通道PB14   1通道PB15,,来自WS2812_POS_e
  * @retval None
  */
void WS2812LoadColor(uint32_t *color,WS2812_POS_e pos)
{
    uint32_t ctemp;
    int i,j=0;
    
    if(pos == WS2812_DOWN)
    {
        /* 测试DMA第一个传输的数据需要全设置为0 */
        for(j=0;j<72;j++)
        {
            wsgpio1[j]   = 0x40000000U;
        }
        
        for(i =1;i<(PIX_NUM_DOWN+1);i++)
        {
            ctemp=*(color+i-1);
            
            for(j=0;j<24;j++)
            {
                if(ctemp&0x800000)
                {
                    wsgpio1[72*i+j*3]   = 0x00004000U;
                    wsgpio1[72*i+j*3+1] = 0x00004000U;
                    wsgpio1[72*i+j*3+2] = 0x40000000U;  
                }
                else
                {
                    wsgpio1[72*i+j*3]   = 0x00004000U;
                    wsgpio1[72*i+j*3+1] = 0x40000000U;
                    wsgpio1[72*i+j*3+2] = 0x40000000U;
                }
                ctemp <<= 1;   
            }
        }
    }
    else if(pos == WS2812_UP)
    {
        /* 测试DMA第一个传输的数据需要全设置为0 */
        for(j=0;j<72;j++)
        {
            wsgpio2[j]   = 0x40000000U;
        }
        
        for(i =1;i<(PIX_NUM_UP+1);i++)
        {
            ctemp=*(color+i-1);
            
            for(j=0;j<24;j++)
            {
                if(ctemp&0x800000)
                {
                    wsgpio2[72*i+j*3]   = 0x00008000U;
                    wsgpio2[72*i+j*3+1] = 0x00008000U;
                    wsgpio2[72*i+j*3+2] = 0x80000000U;  
                }
                else
                {
                    wsgpio2[72*i+j*3]   = 0x00008000U;
                    wsgpio2[72*i+j*3+1] = 0x80000000U;
                    wsgpio2[72*i+j*3+2] = 0x80000000U;
                }
                ctemp <<= 1;   
            }
        }
    }
}
#endif

#if USE_TIMER_PWM_DMA 
/**
  * @brief WS2812显示加载的数据颜色
  * @param  *color 需要显示的颜色数组	
  * @retval None
  */
void WS2812ShowColor(void)
{
	WS2812LoadColor(WS2812ShowBuffer);
    HAL_TIM_PWM_Start_DMA(&WS2812_TIMER_HANDLE, WS2812_TIMER_CHANNEL, (uint32_t *)wstx, 24*PIX_NUM);
}
#endif

#if USE_GIPO_TIMER_DMA
/**
  * @brief WS2812显示加载的数据颜色
  * @param  *color 需要显示的颜色数组
  * @param  ch 需要加载颜色的通道，0，通道PB14   1通道PB15,来自 WS2812_POS_e
  * @retval None
  */
void WS2812ShowColor(uint32_t *color,WS2812_POS_e pos)
{
	WS2812LoadColor(color,pos);
    if(pos == WS2812_DOWN)
    {
        HAL_DMA_Start_IT(&WS2812_GPIO_DMA_Handle, (uint32_t)wsgpio1, (uint32_t)&GPIOB->BSRR, 72*(PIX_NUM_DOWN+1));
    }
    else if(pos == WS2812_UP)
    {
        HAL_DMA_Start_IT(&WS2812_GPIO_DMA_Handle, (uint32_t)wsgpio2, (uint32_t)&GPIOB->BSRR, 72*(PIX_NUM_UP+1));
    }
}
#endif

/**
  * @brief  WS2812 PWM脉冲完成回调函数
  *         需要在HAL_TIM_PWM_PulseFinishedCallback函数中调用
  * @param  None
  * @retval None
  */
void WS2812_TIM_PWM_PulseFinishedCallback(TIM_HandleTypeDef *htim)
{
    /* 采用PB8单通道控制灯带 */
	if(htim->Instance == WS2812_TIMER)
    {
        /* 一定要将占空比设置为0清零不然会出现很多显示错误 */
        __HAL_TIM_SetCompare(&WS2812_TIMER_HANDLE, WS2812_TIMER_CHANNEL, 0);
        HAL_TIM_PWM_Stop_DMA(&WS2812_TIMER_HANDLE, WS2812_TIMER_CHANNEL);
    }
}

/**
  * @brief  HAL库定时器PWM脉冲完成回调函数
  * @param  *color 颜色数组指针
  *         dir：移动的方向，LEFT 左移，RIGHT 右移
  * @retval None
  */
void MoveColorBuff(uint32_t *color,uint8_t dir)
{
	uint32_t ctemp;
	int i;
    /* 左移 */
	if(dir==LEFT)
	{
		ctemp=*(color+PIX_NUM_DOWN-1);	
		for(i=PIX_NUM_DOWN-1;i>0;i--)
		{
			*(color+i)=*(color+i-1);
		}
		*color=ctemp;
	}
    /* 右移 */
	else if(dir==RIGHT)
	{
		ctemp=*color;
		for(i=0;i<PIX_NUM_DOWN-1;i++)
		{
			*(color+i)=*(color+i+1);
		}
		*(color+PIX_NUM_DOWN-1)=ctemp;			
	}
}

/**
  * @brief  HAL库定时器PWM脉冲完成回调函数
  * @param  None
  * @retval None
  */
void HAL_TIM_PWM_PulseFinishedCallback(TIM_HandleTypeDef *htim)
{
    /* WS2812 PWM脉冲完成回调函数 */
    WS2812_TIM_PWM_PulseFinishedCallback(htim);
}

/**
  * @brief  WS2812流水灯模式
  * @param  mode 模式 WS2812_WORK_MODE_e 
  * @retval None
  */
void WS2812_SetWorkMode(uint8_t mode)
{
    WS2812_MODE = mode;
}

/**
  * @brief  让下彩灯显示电量
  * @param  lastPixColor:最后一个灯珠是否显示，0不显示，1显示
  * @retval None
  */
void ShowPowerModeDownRGB(uint8_t lastPixColor)
{
    uint8_t power;
    uint8_t greenNum;
    power = Sensor_GetBattery().power;
    /* 显示绿色的灯珠数量 */
    greenNum = (int)((power/100.0f) * PIX_NUM_DOWN + 0.5f);

    for(int i = 0;i<greenNum;i++)
    {
        WS2812ShowBuffer_DOWN[i] = WS_LIGHT_GREEN;
    }
    for(int i = greenNum; i<PIX_NUM_DOWN -1 ;i++)
    {
        WS2812ShowBuffer_DOWN[i] = WS_LIGHT_WHITEH;
    }

    if(lastPixColor == 0)
    {
        WS2812ShowBuffer_DOWN[PIX_NUM_DOWN-1] = WS_BLACK;
    }
    else
    {
        WS2812ShowBuffer_DOWN[PIX_NUM_DOWN-1] = WS_LIGHT_WHITEH;
    }
}

/**
  * @brief  实验室定义的彩灯控制模式，放入1ms处理函数
  * @param  None
  * @retval None
  */
void WS2812_IUSL_Pro_1ms(void)
{
    static uint16_t WS2812TimeCount1 = 0, WS2812TimeCount2 = 10;
    static uint16_t breathTimeCount = 0;
    
    WS2812TimeCount1++;
    WS2812TimeCount2++;

    if(breathTimeCount++ > 7)
    {
        /* 如果处于正在上电中 */
        if(Robot_GetStatus().Power_Status == POWERING)
        {
            static uint8_t r,g,b,flag;
            /* 让上下灯带显示呼吸灯模式 */
            if(flag == 0)
            {
                r++;
                g++;
                b++;
            }
            else if(flag == 1)
            {
                r--;
                g--;
                b--;
            }
            if(r == 180)
            {
                flag = 1;
            }
            else if(r == 0)
            {
                flag = 0;
            }
            for(int i = 0;i<PIX_NUM_DOWN;i++)
            {
                WS2812ShowBuffer_DOWN[i] = (g<<16)+(r<<8)+(b);
            }
            for(int i = 0;i<PIX_NUM_UP;i++)
            {
                WS2812ShowBuffer_UP[i] = (g<<16);
            }
        }
        /* 如果上电完成 */
        else if(Robot_GetStatus().Power_Status == POWERED)
        {
            static uint8_t r,g,b,flag;
            static uint16_t lastPixColorCount = 0;
            static uint8_t lastPixColor;
            /* 让上下灯带显示呼吸灯模式 */
            if(flag == 0)
            {
                r++;
                g++;
                b++;
            }
            else if(flag == 1)
            {
                r--;
                g--;
                b--;
            }
            if(r == 180)
            {
                flag =1;
            }
            else if(r == 0)
            {
                flag =0;
            }
            /* 设置上灯带 */
            for(int i = 0;i<PIX_NUM_DOWN;i++)
            {
                /* 如果未建立通信 */
                if(Robot_GetStatus().Commut_Status == NO_COMMUT)
                {
                    /* 设置上灯带显示数组 */
                    WS2812ShowBuffer_UP[i] = (g<<16);
                }
                /* 如果建立了通信 */
                else if(Robot_GetStatus().Commut_Status == COMMUTED)
                {
                    /* IMU异常蓝色呼吸 */
                    if(Sensor_GetStatus().IMU_Status == SENSOR_ERROR)
                    {
                        WS2812ShowBuffer_UP[i] = b;
                    }
                    /* 摄像头异常黄色呼吸 */
                    else if(Sensor_GetStatus().CAM_Status == SENSOR_ERROR)
                    {
                        WS2812ShowBuffer_UP[i] = (g<<16)+(r<<8);
                    }
                }
            }

            /* 如果电量过低 */
            if(Sensor_GetStatus().Battery_Status == BAT_LOW_POWER)
            {
                for(int i = 0;i<PIX_NUM_DOWN;i++)
                {
                    WS2812ShowBuffer_DOWN[i] = (r<<8);
                } 
            }
            /* 如果电量正常 */
            else if(Sensor_GetStatus().Battery_Status == BAT_ENOUGH_POWER)
            {
                if(lastPixColorCount++>70)
                {
                    lastPixColor = !lastPixColor;
                    lastPixColorCount = 0;
                }
                /* 如果未通信成功则显示电量模式 */
                if(Robot_GetStatus().Commut_Status == NO_COMMUT)
                {
                    ShowPowerModeDownRGB(lastPixColor);
                }
                /* 如果通信成功 */
                else if(Robot_GetStatus().Commut_Status == COMMUTED)
                {
                    /* 如果IMU错误或摄像头错误 */
                    if(Sensor_GetStatus().CAM_Status == SENSOR_ERROR || Sensor_GetStatus().IMU_Status == SENSOR_ERROR)
                    {
                        ShowPowerModeDownRGB(lastPixColor);
                    }
                    /* 如果无异常 */
                    else 
                    {
                        /* 如果收到灯光控制指令 */
                        if(Robot_GetStatus().RGB_Cmd_Rev_Status == REV_RGB_CMD)
                        {
                            
                        }
                        /* 如果未收到灯光控制指令,则继续显示当前电量 */
                        else if(Robot_GetStatus().RGB_Cmd_Rev_Status == NO_REV_RGB_CMD)
                        {
                            ShowPowerModeDownRGB(lastPixColor);
                        }
                    }
                }
            }
        } 
        breathTimeCount = 0;     
    }
    
    /* 每隔10ms通过DMA轮流刷新上彩灯和下彩灯 */
    if(WS2812TimeCount1 % 20 == 0)
    {
        WS2812ShowColor(WS2812ShowBuffer_UP,WS2812_UP);
    }
    else if(WS2812TimeCount2 % 20 == 0)
    {
        WS2812ShowColor(WS2812ShowBuffer_DOWN,WS2812_DOWN);
    }
}
