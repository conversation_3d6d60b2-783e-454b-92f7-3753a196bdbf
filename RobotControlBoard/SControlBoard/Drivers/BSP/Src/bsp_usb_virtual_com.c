/*! ----------------------------------------------------------------------------
 * @file    bsp_uab_virtual_com.c
 * 
 * @version V1.00 
 *
 * @brief   此程序为USB虚拟串口文件，采用RingBuffer收发数据。
 *
 *          CubeMX参数设置
 *          1.Connectivity中USB_OTG_HS选择Device_Only。
 *          2.Middleware的USB_DEVICE Mode选择Communication Device Class (Virtual Port Com)
 *        
 *          使用步骤：
 *          1.调用VirtualComRingBufferInit()函数初始化RingBuffer。
 *          2.将VirtualComTxRingBufferCheckSend()放入周期执行函数中，如bsp_RunPer10ms()用于虚拟串口数据发送
 *          3.将VirtualComRxRingBufferWrite()函数放入usbd_cdc_if.c文件中的CDC_Receive_HS()函数中用于虚拟串口数据接收
 *          4.可以在任务中循环执行VirtualComCheckUpdate()函数，用于检测是否接收到数据并执行相应程序
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/11/24   DSS      发布
 *
 */
#include "bsp_usb_virtual_com.h"
#include "bsp_ringBuffer.h"
#include "usbd_cdc_if.h"

/* 定义usb虚拟串口的的RingBuffer */
ringbuffer_t virtualComTxRingBuffer;
ringbuffer_t virtualComRxRingBuffer;

/* 定义usb虚拟串口的RingBuffer数组 */
uint8_t VirtualComTxRingBuffer[USB_RING_BUFFER_SIZE];
uint8_t VirtualComRxRingBuffer[USB_RING_BUFFER_SIZE];

/* 定义usb虚拟串口的Buffer数组 */
uint8_t VirtualComTxBuffer[USB_BUFFER_SIZE];
uint8_t VirtualComRxBuffer[USB_BUFFER_SIZE];

/**
  * @brief  虚拟串口RingBuffer初始化函数
  * @param  None
  * @retval None
  */
void VirtualComRingBufferInit(void)
{
    ringbuffer_init(&virtualComTxRingBuffer, VirtualComTxRingBuffer, RING_BUFFER_SIZE);
    ringbuffer_init(&virtualComRxRingBuffer, VirtualComRxRingBuffer, RING_BUFFER_SIZE);
}

/**
  * @brief  向USB虚拟串口的VirtualComTxRingBuffer写入数据
  * @param  *data 需要写入的数据
  *         Size 需要写入的数据大小
  * @retval None
  */
void VirtualComTxRingBufferWrite(uint8_t *data, uint16_t Size)
{
    ringbuffer_in(&virtualComTxRingBuffer, data, Size);      
}

/**
  * @brief  检测是否USB虚拟串口的TxRingBuffer中有数据，有数据则发送，使用时需放入周期调用函数中，如bsp_RunPer10ms()
  * @param  None
  * @retval None
  */
void VirtualComTxRingBufferCheckSend(void)
{
    uint16_t usedSize = ringbuffer_getUsedSize(&virtualComTxRingBuffer);
    /* 如果有数据未发送 */
    if(usedSize>0)
    {
        ringbuffer_out(&virtualComTxRingBuffer, VirtualComRxBuffer, usedSize);
        CDC_Transmit_HS(VirtualComRxBuffer, usedSize);
    }
}

/**
  * @brief  向USB虚拟串口的VirtualComRxRingBuffer写入数据
  * @param  *data 需要写入的数据
  *         Size 需要写入的数据大小
  * @retval None
  */
void VirtualComRxRingBufferWrite(uint8_t *data, uint16_t Size)
{
    ringbuffer_in(&virtualComRxRingBuffer, data, Size);      
}

/**
  * @brief  检测虚拟串口RxRingBuffer是否有数据更新，放入任务循环
  * @param[out]  *readData:读取的数据指针
  * @param[out]  *len:读取的数据长度指针
  * @retval 有数据更新返回1，否则返回0
  */
uint8_t VirtualComCheckUpdate(uint8_t *readData,uint16_t *len)
{
    if(ringbuffer_isEmpty(&virtualComRxRingBuffer)==0)
    {
        uint16_t size = ringbuffer_getUsedSize(&virtualComRxRingBuffer);
        ringbuffer_out(&virtualComRxRingBuffer, readData, size);
        *len = size;
        return 1;
    }
    return 0;
}





