/*! ----------------------------------------------------------------------------
 * @file    bsp_vicon.c
 * 
 * @version V1.00 
 *
 * @brief   此程序中获取VICON的坐标数据
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2023/07/04   DSS      发布
 *
 */
 
#include "bsp_vicon.h"


ViconData_t ViconData;

/**
  * @brief  设置Vicon数据
  * @param  None
  * @retval None
  */
void SetViconData(ViconData_t *data)
{
    ViconData = *data;
}

/**
  * @brief  机器人控制处理程序,每10ms处理一次
  * @param  None
  * @retval None
  */
ViconData_t GetViconData(void)
{
    return ViconData;
}

