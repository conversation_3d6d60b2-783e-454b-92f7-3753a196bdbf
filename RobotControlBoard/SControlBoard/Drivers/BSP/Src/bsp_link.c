/*! ----------------------------------------------------------------------------
 * @file    bsp_link.c
 * 
 * @version V1.00 
 *
 * @brief   板级驱动源文件
 *          文件中包含了板级驱动函数定义
 *
 *          使用步骤：
 *          1.Link_Init()初始化
 *          2.Link_SendIMU()发送数据
 *          3.Link_Receive()接收数据
 *      
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2023/06/15   mz&DSS    实验室自定义通信协议
 *
 */

#include "bsp.h"
#include "bsp_link.h"
#include "bsp_mpu_sensor.h"
#include "bsp_usb_virtual_com.h"
#include "bsp_motor.h"
#include "bsp_print.h"
#include "usart.h"
#include "bsp_battery.h"
#include "sensor.h"
#include "robot.h"
#include "math.h"
#include "bsp_uart.h"
#include "RobotMath.h"
#include "RobotMove.h"
#include "bsp_usb_virtual_com.h"
#include "bsp_motor.h"
#include "MoveDriverPort.h"
#include "RobotMove.h"
#include "bsp_vicon.h"
#include "bsp_ws2812.h"
#include "bsp_beep.h"

/* 发送帧头和地址 */
#define SFRAME_HEAD	0xFE
#define SFRAME_ADDR	0x55
/* 接收帧头和地址 */
#define RFRAME_HEAD	0xFE
#define RFRAME_ADDR	0xAA


typedef void (*Link_DataDecode)(void);
static void Link_Check(link_t *link);
static void Link_Decode(void);
static void Link_AddSend(link_t *link);
static void Link_SendIMU(void);
static void Link_SendOdometry(void);
static void Link_SendHeart(void);
static void Link_SendGPS(void) ;


#define BYTE0(data)	(*((char *)(&data)))
#define BYTE1(data)	(*((char *)(&data) + 1))
#define BYTE2(data)	(*((char *)(&data) + 2))
#define BYTE3(data)	(*((char *)(&data) + 3))	

link_t 		receive;
link_t 		send;

/**
  * @brief  数据接收函数
  * @brief  帧头1 55AA 设备地址：1参考硬件定义 功能码1ID 数据长度1Len 数据内容n  数据校验2 
  * @retval 无
  */
void Link_Receive(uint8_t data)
{
    static uint16_t revStep = 0;
	/* 帧头 */
	if (revStep < (receive.headLen + receive.addrLen) &&
        data == *((uint8_t *)&receive.head + revStep))
    {
        revStep++;
    }
	/* 接收功能码 接受完帧头与地址 */
    else if (revStep == (receive.headLen + receive.addrLen))
    {
        receive.cmd = data;
        revStep++;
    }
	/* 接收数据长度 */
    else if (revStep == (receive.headLen + receive.addrLen) + 1)
    {
        receive.dataLen = data;
		receive.length = receive.length + receive.dataLen;
        revStep++;
    }
	/* 接收数据 数据长度位到数据最后一个字节*/
    else if (revStep > (receive.headLen + receive.addrLen) + 1 &&
             revStep < (receive.headLen + receive.addrLen) + receive.dataLen + 2)
    {
		/* 数据位 */
        *((uint8_t *)&receive.head + revStep) = data;
        revStep++;
    }
	/* 接收数据校验位 */
    else if (revStep == (receive.headLen + receive.addrLen) + receive.dataLen + 2)
    {
        receive.sumCheck = data;
        revStep++;
    }
	/* 接收数据校验位 */
    else if (revStep == (receive.headLen + receive.addrLen) + receive.dataLen + 3)
    {
        receive.addCheck = data;
		/* 接收完数据并解析 */
		Link_Decode();
		revStep = 0;
        receive.dataLen = 0;
    }
	/* 无关项 */
    else
    {
        revStep = 0;
        receive.dataLen = 0;
    }
	receive.errorRate = (float)receive.checkNum[1]/(receive.checkNum[0] + receive.checkNum[1]);
}

/**
 * @brief  解析函数 将收到的一包数据进行解析
 */
static void Link_Decode(void)
{
	static uint8_t sumCheck,addCheck;
	/* 记录接收校验 */
	sumCheck = receive.sumCheck;
	addCheck = receive.addCheck;
	/* 校验 */
	Link_Check(&receive);
	/* 校验错误 */
	if (receive.sumCheck != sumCheck || receive.addCheck != addCheck)
	{
		receive.checkNum[1]++;
        return;
	}
	/* 接收记录 */
	receive.checkNum[0]++;
	
    /* 移动模式设置指令，0电机控制模式，并设置电机转速为0，即小车停止，1进入速度控制模式，小车启动 */
    if(receive.cmd == MOVE_MODE_CMD)
    {
        /* 接收到0，设置为电机控制模式 */
        if(receive.dataBuf[0] == 0)
        {
            RobotSetControlMode(MOTOR_CONTROL);
            /* 将四个电机PWM设置为0 */
            SetRobotMotorPWM(LeftUp,0);
            SetRobotMotorPWM(LeftDown,0);
            SetRobotMotorPWM(RightUp,0);
            SetRobotMotorPWM(RightDown,0);
        }
        /* 接收到1，设置为速度控制模式，通过Vx,Vy,Wz控制 */
        else if(receive.dataBuf[0] == 1)
        {
            RobotSetControlMode(MOVE_CONTROL);
            RobotMove(0,0,0);
        }
    }
    /* 速度控制指令 */
	else if(receive.cmd == SPEED_CTL_CMD)
	{
        float vx,vy,wz;
        RobotSetControlMode(MOVE_CONTROL);
        
        /* 进行机器人投喂，如果超过 MAX_ROBOT_FEED_TIME 投喂时间，则小车会停止 */
        FeedRobot();
        
        memcpy(&vx,receive.dataBuf,4);
        memcpy(&vy,receive.dataBuf+4,4);
        memcpy(&wz,receive.dataBuf+8,4);
        
        /* 控制速度 */
        RobotMove(vx,vy,wz);
    }
    /* VICON数据指令 */
	else if(receive.cmd == VICON_DATA_MSG)
	{
        FeedDog_VICON();
        ViconData_t vdata;
        memcpy(&vdata.AngleX,receive.dataBuf,4);
        memcpy(&vdata.AngleY,receive.dataBuf+4,4);
        memcpy(&vdata.AngleZ,receive.dataBuf+8,4);
        memcpy(&vdata.PosX,receive.dataBuf+12,4);
        memcpy(&vdata.PosY,receive.dataBuf+16,4);
        memcpy(&vdata.PosZ,receive.dataBuf+20,4);
        
        SetViconData(&vdata);
    }
    /*  收到心跳包数据 */
    else if(receive.cmd == HEART_MSG)
    {
        /* 如果未建立通信 */
        if(Robot_GetStatus().Commut_Status == NO_COMMUT)
        {
            /* 让蜂鸣器长响1S */
            BeepStart(100, 5, 1);           
            /* 收到心跳包数据后让上灯带全部显示黑色 */
            WS2812ShowSameColor(WS_BLACK,WS2812_UP); 
        }
        FeedDog_Heart();
        /* 设置ID */
        SetRobotID(receive.dataBuf[0]); 
        /* 设置摄像头状态 */
        if(receive.dataBuf[1] == 0)
        {
           SensorStatus.CAM_Status = SENSOR_ERROR;
        }
        else if(receive.dataBuf[1] == 1)
        {
            SensorStatus.CAM_Status = SENSOR_OK;
        }
    }
    /* 上灯带控制 */
    else if(receive.cmd == UP_RGB_CMD)
    {
        if(Robot_GetStatus().Commut_Status == COMMUTED)
        {
            uint8_t pixNum = 0;
            uint32_t color = 0;
            uint8_t pixCount =0;
            uint8_t pixNumAll = 0; /* 控制的总灯珠数量 */
            
            /* 清除显示数组 */
            WS2812_ClearShowBuffer(WS2812_UP);
            
            FeedDog_RGB();
            
            for(int i = 0;i < receive.dataLen/4; i++)
            {
                pixNum = receive.dataBuf[i*4 + 3];
                pixNumAll += pixNum;
                
                /* 判断灯珠数量 */
                if(pixNumAll > PIX_NUM_UP)
                {
                    /* 如果数量超了，则超的部分不显示 */
                    if(pixNumAll - pixNum > PIX_NUM_UP)
                    {
                        pixNum = 0;
                    }
                    else 
                    {
                        pixNum = PIX_NUM_UP - (pixNumAll - pixNum);
                    }
                }
                
                memcpy(&color,receive.dataBuf + i*4,3);
                
                for(int j = 0; j < pixNum; j++)
                {
                    WS2812ShowBuffer_UP[pixCount] = color;

                    pixCount++;
                }
            }   
        }        
    }
    /* 下灯带控制 */
    else if(receive.cmd == DOWN_RGB_CMD)
    {
        if(Robot_GetStatus().Commut_Status == COMMUTED)
        {
            uint8_t pixNum;
            uint32_t color = 0;
            uint8_t pixCount =0;
            uint8_t pixNumAll = 0; /* 控制的总灯珠数量 */
            
            /* 如果电量足够接收下灯带控制指令，电量不足则不接受下灯带控制指令 */
            if(Sensor_GetStatus().Battery_Status == BAT_ENOUGH_POWER)
            {    
                FeedDog_RGB();
                
                /* 清除显示数组 */
                WS2812_ClearShowBuffer(WS2812_DOWN);
                
                for(int i = 0;i<receive.dataLen/4;i++)
                {
                    pixNum = receive.dataBuf[i*4+3];
                    pixNumAll += pixNum;
                    
                     /* 判断灯珠数量 */
                    if(pixNumAll > PIX_NUM_DOWN)
                    {
                        /* 如果数量超了，则超的部分不显示 */
                        if(pixNumAll - pixNum > PIX_NUM_DOWN)
                        {
                            pixNum = 0;
                        }
                        else 
                        {
                            pixNum = PIX_NUM_DOWN - (pixNumAll - pixNum);
                        }
                    }
                    
                    memcpy(&color,receive.dataBuf + i*4,3);
                    
                    for(int j = 0; j<pixNum; j++)
                    {
                        WS2812ShowBuffer_DOWN[pixCount] = color;
                        pixCount++;
                    }
                }
            }
        }
    }
}

/**
  * @brief  校验函数
  * @retval 无
  */
static void Link_Check(link_t *link)
{
    link->sumCheck = 0;
    link->addCheck = 0;
    for (size_t i = 0; i < 4; i++)
    {
        link->sumCheck += *(uint8_t *)(&link->head + i);
        link->addCheck += link->sumCheck;
    }
    
    for (size_t i = 0; i < link->dataLen; i++)
    {
        link->sumCheck += link->dataBuf[i];
        link->addCheck += link->sumCheck;
    }
}

/**
  * @brief  初始化函数
  * @retval 无
  */
void Link_Init(void)
{
    send.head = SFRAME_HEAD;
	/* 暂时作为主机模式 */
    send.addr = SFRAME_ADDR;
	send.headLen = 1;
	send.addrLen = 1;
	
    receive.head = RFRAME_HEAD;	
    receive.addr = RFRAME_ADDR;
    receive.headLen = 1;
    receive.addrLen = 1;
	/* 测试用 */
}

/**
  * @brief  发送循环 定时分包发送数据,放入1ms周期定时器
  * @retval 无
  */
void Link_SendLoop(void)
{
	static uint32_t tick = 0;
	tick++;
    /* 100HZ */
	if(tick % 10 == 0)
	{
		Link_SendIMU();
	}
    /* 10HZ */
	if(tick % 100 == 0)
	{
        Link_SendOdometry();
        if(SensorStatus.GPS_Status == SENSOR_OK)
        {
            Link_SendGPS();
        }  
	}
    /* 1HZ */
	if(tick % 1000 == 0)
	{
        Link_SendHeart();
	}
}

/**
  * @brief  协议发送函数
  * @brief  压入环形缓冲区等待处理
  * @retval 无
  */
static void Link_AddSend(link_t *link)
{
	uint8_t buf[128];
	/* 获取校验数据 */
	Link_Check(link);
	/* 拷贝帧头等数据 */
	memcpy(buf, (uint8_t *)&link->head, 4);
	/* 拷贝数据 */
	memcpy(buf + 4, (uint8_t *)link->dataBuf, link->dataLen);
	/* 拷贝校验 */
	memcpy(buf + 4 + link->dataLen, (uint8_t *)&link->sumCheck, 2);
	/* 压入FIFO */
	VirtualComTxRingBufferWrite(buf, link->dataLen + 6);
}

/**
  * @brief  发送心跳包数据
  * @param  None
  * @retval 无
  */
void Link_SendHeart(void) 
{
    uint8_t power;
    uint8_t sta_imu;
    uint8_t sta_gps;
    send.cmd = HEART_MSG;
    
    /* 电量信息 */
    power = (int)(Sensor_GetBattery().power + 0.5);
    sta_imu = Sensor_GetStatus().IMU_Status;
    sta_gps = Sensor_GetStatus().GPS_Status;
    
	send.dataLen = 3;
    
    send.dataBuf[0] = power;
    send.dataBuf[1] = sta_imu;
    send.dataBuf[2] = sta_gps;
    
    /* 数据发送 */
	Link_AddSend(&send);
}

/**
  * @brief  发送IMU数据
  * @param  None
  * @retval 无
  */
void Link_SendIMU(void) 
{
    float ax,ay,az,gx,gy,gz,q0,q1,q2,q3;
	send.cmd = IMU_MSG;
	send.dataLen = 40;
    
    /* 相乘的系数和传感器量程设置有关，当前设置±2g量程 */
    ax= 2.0f*(Sensor_GetIMU().xacc/32767.0f); 
    ay= 2.0f*(Sensor_GetIMU().yacc/32767.0f); 
    az= 2.0f*(Sensor_GetIMU().zacc/32767.0f);
    
    /* 相乘的系数和传感器量程设置有关，当前设置±2000dps量程 */
    gx = 2000.0f*(Sensor_GetIMU().xgyro/32767.0f); 
    gy = 2000.0f*(Sensor_GetIMU().ygyro/32767.0f); 
	gz = 2000.0f*(Sensor_GetIMU().zgyro/32767.0f); 
    
    q0 = Sensor_GetIMU().qw;
    q1 = Sensor_GetIMU().qx;
    q2 = Sensor_GetIMU().qy;
    q3 = Sensor_GetIMU().qz;
    
	/* 填充发送数组 */
    memcpy(send.dataBuf,&ax,4);
    memcpy(send.dataBuf+4,&ay,4);
    memcpy(send.dataBuf+8,&az,4);
    memcpy(send.dataBuf+12,&gx,4);
    memcpy(send.dataBuf+16,&gy,4);
    memcpy(send.dataBuf+20,&gz,4);
    memcpy(send.dataBuf+24,&q0,4);
    memcpy(send.dataBuf+28,&q1,4);
    memcpy(send.dataBuf+32,&q2,4);
    memcpy(send.dataBuf+36,&q3,4);
    
    /* 数据发送 */
	Link_AddSend(&send);
}

/**
  * @brief  发送编码器数据
  * @param  None
  * @retval 无
  */ 
void Link_SendOdometry(void) 
{
    float vx,vy,wz,disx,disy,yawall;
	send.cmd = ODOMETRY_MSG;
	send.dataLen = 24;
    
    /* 获取编码器计算的Vx Vy和yawSpeed */
    vx= Sensor_GetOdometry().vx; 
    vy= Sensor_GetOdometry().vy; 
    wz= Sensor_GetOdometry().yawSpeed;
    
    /* 相乘的系数和传感器量程设置有关，当前设置±2000dps量程 */
    disx = Sensor_GetOdometry().disX_WCS; 
    disy = Sensor_GetOdometry().disY_WCS; 
	
    yawall = Sensor_GetOdometry().yawAll;
    
	/* 填充发送数组 */
    memcpy(send.dataBuf,&vx,4);
    memcpy(send.dataBuf+4,&vy,4);
    memcpy(send.dataBuf+8,&wz,4);
    memcpy(send.dataBuf+12,&disx,4);
    memcpy(send.dataBuf+16,&disy,4);
    memcpy(send.dataBuf+20,&yawall,4);
    
    /* 数据发送 */
	Link_AddSend(&send);
}

/**
  * @brief  发送GPS数据
  * @param  None
  * @retval 无
  */ 
void Link_SendGPS(void) 
{
    float mx = 0,my = 0,mz = 0;
    double longitude = 0,latitude = 0;
    mx = Sensor_GetIMU().xmag;
    my = Sensor_GetIMU().ymag;
    mz = Sensor_GetIMU().zmag;
    longitude = Sensor_GetGpsData().longitude;
    latitude = Sensor_GetGpsData().latitude;
    
    /* 填充发送数组 */
    memcpy(send.dataBuf,&mx,4);
    memcpy(send.dataBuf+4,&my,4);
    memcpy(send.dataBuf+8,&mz,4);
    memcpy(send.dataBuf+12,&longitude,8);
    memcpy(send.dataBuf+20,&latitude,8);
    
    /* 数据发送 */
	Link_AddSend(&send);
}
