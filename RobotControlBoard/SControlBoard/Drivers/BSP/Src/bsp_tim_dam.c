/*! ----------------------------------------------------------------------------
 * @file    bsp_tim_dma.c
 * 
 * @version V1.00 
 *
 * @brief   DMAMUX的定时器触+DMA双缓冲控制任意IO做PWM和脉冲数控制，用于彩灯控制
 *           
 *          使用步骤：
 *          1.调用TIM12_Config()函数初始化定时器输出脉冲
 *          2.调用bsp_InitTimDMA()初始化DMA配置
 * 
 * @Modify
 *
 *      版本号     日期      作者          说明
 *      V1.00   2018/12/12   安富莱电子    发布
 *              2023/06/03   DSS           移植到彩灯控制
 *
 * <AUTHOR>
 */
#include "bsp.h"
#include "bsp_tim_dma.h"
#include "bsp_print.h"
#include "bsp_ws2812.h"


DMA_HandleTypeDef WS2812_GPIO_DMA_Handle = {0};


/**
  * @brief  配置TIM12，用于触发DMAMUX的请求发生器
  * @param  _Mode 
  *         0 表示配置为100KHz触发频率,配置为100KHz触发频率，如果DMAMUX配置为单边沿触发，那么输出PWM频
  *           率是50KHz，双边沿是100KHz。
  *         1 表示配置为10KHz触发频率，如果DMAMUX配置为单边沿触发，那么输出PWM频率是5KHz，双边沿是10KHz。	
  * @retval None
  */
void TIM12_Config(uint8_t _Mode)
{
    TIM_HandleTypeDef  htim ={0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_OC_InitTypeDef sConfig = {0};
    uint32_t Period[2] = {1399, 115};
    uint32_t Pulse[2]  = {699, 57};
  	/* 使能时钟 */  
  	__HAL_RCC_TIM12_CLK_ENABLE();
      
    HAL_TIM_Base_DeInit(&htim);
    
    htim.Instance = TIM12;
	htim.Init.Period            = Period[_Mode];
	htim.Init.Prescaler         = 0;
	htim.Init.ClockDivision     = 0;
	htim.Init.CounterMode       = TIM_COUNTERMODE_UP;
	htim.Init.RepetitionCounter = 0;
	HAL_TIM_Base_Init(&htim);
    
    sConfig.OCMode     = TIM_OCMODE_PWM1;
    sConfig.OCPolarity = TIM_OCPOLARITY_LOW;

    /* 占空比50% */
    sConfig.Pulse = Pulse[_Mode];  
    if(HAL_TIM_OC_ConfigChannel(&htim, &sConfig, TIM_CHANNEL_1) != HAL_OK)
    {
		Error_Handler();
    }

    /* 启动OC1 */
    if(HAL_TIM_OC_Start(&htim, TIM_CHANNEL_1) != HAL_OK)
    {
		Error_Handler();
    }
    
    /* TIM12的TRGO用于触发DMAMUX的请求发生器 */
	sMasterConfig.MasterOutputTrigger = TIM_TRGO_OC1REF;
    sMasterConfig.MasterOutputTrigger2 = TIM_TRGO2_RESET;
	sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;

	HAL_TIMEx_MasterConfigSynchronization(&htim, &sMasterConfig);
}

/**
  * @brief  配置DMAMUX的定时器触+DMA双缓冲控制任意IO做PWM和脉冲数控制
  * @param  None
  * @retval None
  */
void bsp_InitTimDMA(void)
{
    GPIO_InitTypeDef  GPIO_InitStruct;

    HAL_DMA_MuxRequestGeneratorConfigTypeDef dmamux_ReqGenParams = {0};

    /* 1.配置PB14 PB15引脚用于彩灯控制引脚 */
    GPIO_InitStruct.Pin = GPIO_PIN_14|GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* 2.DMA配置 */
    __HAL_RCC_DMA1_CLK_ENABLE();
    WS2812_GPIO_DMA_Handle.Instance                 = DMA1_Stream1;            /* 使用的DMA1 Stream1 */
    WS2812_GPIO_DMA_Handle.Init.Request             = DMA_REQUEST_GENERATOR0;  /* 请求类型采用的DMAMUX请求发生器通道0 */  
    WS2812_GPIO_DMA_Handle.Init.Direction           = DMA_MEMORY_TO_PERIPH;    /* 传输方向是从存储器到外设 */  
    WS2812_GPIO_DMA_Handle.Init.PeriphInc           = DMA_PINC_DISABLE;        /* 外设地址自增禁止 */ 
    WS2812_GPIO_DMA_Handle.Init.MemInc              = DMA_MINC_ENABLE;         /* 存储器地址自增使能 */  
    WS2812_GPIO_DMA_Handle.Init.PeriphDataAlignment = DMA_PDATAALIGN_WORD;     /* 外设数据传输位宽选择字，即32bit */     
    WS2812_GPIO_DMA_Handle.Init.MemDataAlignment    = DMA_MDATAALIGN_WORD;     /* 存储器数据传输位宽选择字，即32bit */    
    WS2812_GPIO_DMA_Handle.Init.Mode                = DMA_NORMAL;              /* 普通模式 */   
    WS2812_GPIO_DMA_Handle.Init.Priority            = DMA_PRIORITY_LOW;        /* 优先级低 */  
    WS2812_GPIO_DMA_Handle.Init.FIFOMode            = DMA_FIFOMODE_DISABLE;    /* 禁止FIFO*/
    WS2812_GPIO_DMA_Handle.Init.FIFOThreshold       = DMA_FIFO_THRESHOLD_FULL; /* 禁止FIFO此位不起作用，用于设置阀值 */
    WS2812_GPIO_DMA_Handle.Init.MemBurst            = DMA_MBURST_SINGLE;       /* 禁止FIFO此位不起作用，用于存储器突发 */
    WS2812_GPIO_DMA_Handle.Init.PeriphBurst         = DMA_PBURST_SINGLE;       /* 禁止FIFO此位不起作用，用于外设突发 */
 
    /* 3.初始化DMA */
    if(HAL_DMA_Init(&WS2812_GPIO_DMA_Handle) != HAL_OK)
    {
		Error_Handler();     
    }
    
    /* 4.开启DMA1 Stream1的中断 */
    HAL_NVIC_SetPriority(DMA1_Stream1_IRQn, 4, 0);
    HAL_NVIC_EnableIRQ(DMA1_Stream1_IRQn); 

    
    /* 5.配置DMAMUX */
    dmamux_ReqGenParams.SignalID  = HAL_DMAMUX1_REQ_GEN_TIM12_TRGO;                     /* 请求触发器选择LPTIM2_OUT */
    dmamux_ReqGenParams.Polarity  = HAL_DMAMUX_REQ_GEN_RISING;                          /* 上升沿触发  */
    dmamux_ReqGenParams.RequestNumber = 1;                                              /* 触发后，传输进行1次DMA传输 */

    HAL_DMAEx_ConfigMuxRequestGenerator(&WS2812_GPIO_DMA_Handle, &dmamux_ReqGenParams); /* 配置DMAMUX */
    HAL_DMAEx_EnableMuxRequestGenerator (&WS2812_GPIO_DMA_Handle);                      /* 使能DMAMUX请求发生器 */   
 

    /* 用不到的中断可以直接关闭 */
    DMA1_Stream1->CR &= ~DMA_IT_DME; 
    DMA1_Stream1->CR &= ~DMA_IT_TE;
    DMA1_Stream1->CR &= ~DMA_IT_DME;
    DMA1_Stream1->CR &= ~DMA_IT_HT;
    DMAMUX1_RequestGenerator0->RGCR &= ~DMAMUX_RGxCR_OIE;
    
    TIM12_Config(1);
}

/**
  * @brief  DMA1 Stream1中断服务程序
  * @param  None
  * @retval None
  */
void DMA1_Stream1_IRQHandler(void)
{
	/* 传输完成中断 */
	if((DMA1->LISR & DMA_FLAG_TCIF1_5) != RESET)
	{
		/* 清除标志 */
		DMA1->LIFCR = DMA_FLAG_TCIF1_5;

		/* 当前使用的缓冲0 */
		if((DMA1_Stream1->CR & DMA_SxCR_CT) == RESET)
		{
            HAL_DMA_Abort(&WS2812_GPIO_DMA_Handle);
		}
		/* 当前使用的缓冲1 */
		else
		{

		}
	}

	/* 半传输完成中断 */    
	if((DMA1->LISR & DMA_FLAG_HTIF1_5) != RESET)
	{
		/* 清除标志 */
		DMA1->LISR = DMA_FLAG_HTIF1_5;
	}
	/* 传输错误中断 */
	if((DMA1->LISR & DMA_FLAG_TEIF1_5) != RESET)
	{
		/* 清除标志 */
		DMA1->LISR = DMA_FLAG_TEIF1_5;
	}

	/* 直接模式错误中断 */
	if((DMA1->LISR & DMA_FLAG_DMEIF1_5) != RESET)
	{
		/* 清除标志 */
		DMA1->LISR = DMA_FLAG_DMEIF1_5;
	}
}

/**
  * @brief   DMA1 Stream0中断服务程序
  * @param   None
  * @retval  None
  */
void DMA1_Stream0_IRQHandler(void)
{
	/* 传输完成中断 */
	if((DMA1->LISR & DMA_FLAG_TCIF0_4) != RESET)
	{
		/* 清除标志 */
		DMA1->LIFCR = DMA_FLAG_TCIF0_4;

		/* 当前使用的缓冲0 */
		if((DMA1_Stream0->CR & DMA_SxCR_CT) == RESET)
		{
            HAL_DMA_Abort(&WS2812_GPIO_DMA_Handle);
			/*
				1、当前正在使用缓冲0，此时可以动态修改缓冲1的数据。
				   比如缓冲区0是IO_Toggle，缓冲区1是IO_Toggle1，那么此时就可以修改IO_Toggle1。
				2、变量所在的SRAM区已经通过MPU配置为WT模式，更新变量IO_Toggle会立即写入。
				3、不配置MPU的话，也可以通过Cahce的函数SCB_CleanDCache_by_Addr做Clean操作。
			*/
		}
		/* 当前使用的缓冲1 */
		else
		{
			 /*
			   1、当前正在使用缓冲1，此时可以动态修改缓冲0的数据。
				  比如缓冲区0是IO_Toggle，缓冲区1是IO_Toggle1，那么此时就可以修改IO_Toggle。
			   2、变量所在的SRAM区已经通过MPU配置为WT模式，更新变量IO_Toggle会立即写入。
			   3、不配置MPU的话，也可以通过Cahce的函数SCB_CleanDCache_by_Addr做Clean操作。
			*/

		}
	}

	/* 半传输完成中断 */    
	if((DMA1->LISR & DMA_FLAG_HTIF0_4) != RESET)
	{
		/* 清除标志 */
		DMA1->LISR = DMA_FLAG_HTIF0_4;
	}

	/* 传输错误中断 */
	if((DMA1->LISR & DMA_FLAG_TEIF0_4) != RESET)
	{
		/* 清除标志 */
		DMA1->LISR = DMA_FLAG_TEIF0_4;
	}

	/* 直接模式错误中断 */
	if((DMA1->LISR & DMA_FLAG_DMEIF0_4) != RESET)
	{
		/* 清除标志 */
		DMA1->LISR = DMA_FLAG_DMEIF0_4;
	}
}

/**
  * @brief   DMAMUX的中断服务程序，这里用于处理请求发生器的溢出。
  * @param   None
  * @retval  None
  */
void DMAMUX1_OVR_IRQHandler(void)
{
    if((DMAMUX1_RequestGenStatus->RGSR & DMAMUX_RGSR_OF0) != RESET)
    {
       /* 关闭溢出中断 */
       DMAMUX1_RequestGenerator0->RGCR &= ~DMAMUX_RGxCR_OIE;
       
       /* 清除标志 */
       DMAMUX1_RequestGenStatus->RGCFR = DMAMUX_RGSR_OF0;
    }
}


