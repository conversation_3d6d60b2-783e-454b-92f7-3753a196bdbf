/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2021 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h7xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define POWER_SW_Pin GPIO_PIN_2
#define POWER_SW_GPIO_Port GPIOE
#define POWER_OUT_Pin GPIO_PIN_3
#define POWER_OUT_GPIO_Port GPIOE
#define MPU_CS_Pin GPIO_PIN_0
#define MPU_CS_GPIO_Port GPIOC
#define MAG_CS_Pin GPIO_PIN_1
#define MAG_CS_GPIO_Port GPIOC
#define ADC_BAT_Pin GPIO_PIN_4
#define ADC_BAT_GPIO_Port GPIOC
#define MPU_INT_Pin GPIO_PIN_5
#define MPU_INT_GPIO_Port GPIOC
#define MPU_INT_EXTI_IRQn EXTI9_5_IRQn
#define Motor1_O2_Pin GPIO_PIN_9
#define Motor1_O2_GPIO_Port GPIOE
#define LCD_RESET_Pin GPIO_PIN_10
#define LCD_RESET_GPIO_Port GPIOE
#define Motor2_O2_Pin GPIO_PIN_11
#define Motor2_O2_GPIO_Port GPIOE
#define Motor3_O2_Pin GPIO_PIN_13
#define Motor3_O2_GPIO_Port GPIOE
#define Motor4_O2_Pin GPIO_PIN_14
#define Motor4_O2_GPIO_Port GPIOE
#define LCD_DC_Pin GPIO_PIN_15
#define LCD_DC_GPIO_Port GPIOE
#define LCD_BL_Pin GPIO_PIN_11
#define LCD_BL_GPIO_Port GPIOB
#define Motor1_O1_Pin GPIO_PIN_6
#define Motor1_O1_GPIO_Port GPIOC
#define Motor2_O1_Pin GPIO_PIN_7
#define Motor2_O1_GPIO_Port GPIOC
#define Motor3_O1_Pin GPIO_PIN_8
#define Motor3_O1_GPIO_Port GPIOC
#define Motor4_O1_Pin GPIO_PIN_9
#define Motor4_O1_GPIO_Port GPIOC
#define LED0_Pin GPIO_PIN_2
#define LED0_GPIO_Port GPIOD
#define KEY0_Pin GPIO_PIN_4
#define KEY0_GPIO_Port GPIOD
#define KEY1_Pin GPIO_PIN_5
#define KEY1_GPIO_Port GPIOD
/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
