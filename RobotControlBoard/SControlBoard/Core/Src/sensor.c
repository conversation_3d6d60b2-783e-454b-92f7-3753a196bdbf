/*! ----------------------------------------------------------------------------
 * @file    sensor.c
 * 
 * @version V1.00 
 *
 * @brief   此文件内的函数是关于无人车所有传感器数据读取的
 *
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS      发布
 *
 */

#include "sensor.h"
#include "bsp_mpu_sensor.h"

/* 定义传感器状态结构体 */
SensorStatus_t SensorStatus = 
{
    .CAM_Status = SENSOR_OK,
    .IMU_Status = SENSOR_ERROR,
    .VICON_Status = SENSOR_ERROR,
    .Battery_Status = BAT_ENOUGH_POWER,
    .GPS_Status = SENSOR_ERROR,
    .IMU_Cali_Status = IMU_CALI_ERROR,
};

static IMU_Sensor_t IMU_Sensor;

/**
  * @brief  从全局变量中读取状态
  * @param  None
  * @retval 电池状态结构体
  */
BatteryStatus_t Sensor_GetBattery(void)
{
    return ReadBatteryStatus();
}

/**
  * @brief  读取IMU数据
  * @param  None
  * @retval 电池状态结构体
  */
void Sensor_ReadIMU(void)
{
    if(MPU_SensorCheckUpdata())
    {
        MPU_MPL_ReadData(false,&IMU_Sensor);
    }
}

/**
  * @brief  获取IMU数据
  * @param  None
  * @retval IMU状态结构体
  */
IMU_Sensor_t Sensor_GetIMU(void)
{
    return IMU_Sensor;
}       

/**
  * @brief  获取VICON数据
  * @param  None
  * @retval VICON状态结构体
  */
ViconData_t Sensor_GetVICON(void)
{
    return GetViconData();
} 

/**
  * @brief  获取里程计数据
  * @param  None
  * @retval IMU状态结构体
  */
RobotOdometry_t Sensor_GetOdometry(void)
{
    return RobotGetOdometry();
}

/**
  * @brief  获取电机占空比函数
  * @param  None
  * @retval None
  */
RobotMotorPwm_t Sensor_GetMotorPWM(void)
{
    return GetMotorPWM();
}

/**
  * @brief  获取GPS数据
  * @param  None
  * @retval None
  */
ubloxData_t Sensor_GetGpsData(void)
{
    return GPS_GetData();
}

/**
  * @brief  设置传感器状态
  * @param  None
  * @retval None
  */
void Sensor_SetStatus(SensorStatus_t status)
{
    SensorStatus = status;
}

/**
  * @brief  获取传感器状态
  * @param  None
  * @retval None
  */
SensorStatus_t Sensor_GetStatus(void)
{
   return SensorStatus;
}
