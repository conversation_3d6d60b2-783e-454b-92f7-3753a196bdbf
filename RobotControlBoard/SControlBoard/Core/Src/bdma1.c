/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    bdma1.c
  * @brief   This file provides code for the configuration
  *          of all the requested memory to memory DMA transfers.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "bdma1.h"

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure DMA                                                              */
/*----------------------------------------------------------------------------*/

/* USER CODE BEGIN 1 */

/* USER CODE END 1 */
DMA_HandleTypeDef hdma_memtomem_bdma1_channel0;

/**
  * Enable DMA controller clock
  * Configure DMA for memory to memory transfers
  *   hdma_memtomem_bdma1_channel0
  */
void MX_BDMA1_Init(void)
{

  /* DMA controller clock enable */
  __HAL_RCC_BDMA1_CLK_ENABLE();

  /* Configure DMA request hdma_memtomem_bdma1_channel0 on BDMA1_Channel0 */
  hdma_memtomem_bdma1_channel0.Instance = BDMA1_Channel0;
  hdma_memtomem_bdma1_channel0.Init.Direction = DMA_MEMORY_TO_MEMORY;
  hdma_memtomem_bdma1_channel0.Init.PeriphInc = DMA_PINC_ENABLE;
  hdma_memtomem_bdma1_channel0.Init.MemInc = DMA_MINC_ENABLE;
  hdma_memtomem_bdma1_channel0.Init.PeriphDataAlignment = DMA_PDATAALIGN_WORD;
  hdma_memtomem_bdma1_channel0.Init.MemDataAlignment = DMA_MDATAALIGN_WORD;
  hdma_memtomem_bdma1_channel0.Init.Mode = DMA_NORMAL;
  hdma_memtomem_bdma1_channel0.Init.Priority = DMA_PRIORITY_LOW;
  if (HAL_DMA_Init(&hdma_memtomem_bdma1_channel0) != HAL_OK)
  {
    Error_Handler();
  }

}

/* USER CODE BEGIN 2 */

/* USER CODE END 2 */

