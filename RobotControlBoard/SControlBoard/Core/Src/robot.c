/*! ----------------------------------------------------------------------------
 * @file    robot.c
 * 
 * @version V1.00 
 *
 * @brief   此文件内的函数关于无人车状态相关的函数
 *
 *
 * @Modify
 *
 *      版本号     日期      作者      说明
 *      V1.00   2022/08/02   DSS      发布
 *
 */

#include "robot.h"
#include <stdint.h>
#include <string.h>
#include "bsp_uart.h"
#include "usart.h"
#include "sensor.h"

static uint8_t ROBOT_ID = ROBOT_ID_DEFINE;

/* 定义机器人状态结构体 */
RobotStatus_t RobotStatus = 
{
    .Power_Status = BEFORE_POWER,           /* 还未上电 */
    .Commut_Status = NO_COMMUT,             /* 还未建立连接 */
    .RGB_Cmd_Rev_Status = NO_REV_RGB_CMD,   /* RGB彩灯命令状态 */
};

/* 无线数据发送时钟同步变量 */
uint32_t CommutTimeCount = 0;
/* 无线数据发送标志位，为1时需要发送数据 */
uint32_t CommutSendFlag = 0;

/* 定义数传发送数组 */
uint8_t wirelessSendBuff[20];
/* 心跳包喂狗计时变量 */
uint32_t FeedDogHeartTimeCount;
/* RGB彩灯喂狗计时变量 */
uint32_t FeedDogRGBTimeCount;
/* VICON喂狗计时变量 */
uint32_t FeedDogVICONTimeCount;

/**
  * @brief  设置机器人ID信息
  * @param  None
  * @retval None
  */
void SetRobotID(uint8_t id)
{
    ROBOT_ID = id;
}

/**
  * @brief  设置机器人ID信息
  * @param  None
  * @retval None
  */
uint8_t GetRobotID(void)
{
    return ROBOT_ID;
}

/**
  * @brief  设置机器人当前状态信息
  * @param  None
  * @retval None
  */
void Robot_SetStatus(RobotStatus_t status)
{
    RobotStatus = status;
}

/**
  * @brief  获取机器人当前状态信息
  * @param  None
  * @retval None
  */
RobotStatus_t Robot_GetStatus(void)
{
    return RobotStatus;
}

/**
  * @brief  心跳包喂狗
  * @param  None
  * @retval None
  */
void FeedDog_Heart(void)
{
    FeedDogHeartTimeCount = 0;
    RobotStatus.Commut_Status = COMMUTED;
}

/**
  * @brief  RGB彩灯喂狗
  * @param  None
  * @retval None
  */
void FeedDog_RGB(void)
{
    FeedDogRGBTimeCount = 0;
    RobotStatus.RGB_Cmd_Rev_Status = REV_RGB_CMD;
}

/**
  * @brief  VICON喂狗
  * @param  None
  * @retval None
  */
void FeedDog_VICON(void)
{
    FeedDogVICONTimeCount = 0;
    SensorStatus.VICON_Status = SENSOR_OK;
}

/**
  * @brief  心跳包喂狗处理程序100ms放入定时器
  * @param  None
  * @retval None
  */
void FeedDog_Heart_Pro_100ms(void)
{
    if(FeedDogHeartTimeCount++ > HEART_FEED_DOG_TIME)
    {
        FeedDogHeartTimeCount = 0;
        RobotStatus.Commut_Status = NO_COMMUT;
    }
}

/**
  * @brief  RGB彩灯控制喂狗处理程序100ms放入定时器
  * @param  None
  * @retval None
  */
void FeedDog_RGB_Pro_100ms(void)
{
    if(FeedDogRGBTimeCount++ > RGB_FEED_DOG_TIME)
    {
        FeedDogRGBTimeCount = 0;
        RobotStatus.RGB_Cmd_Rev_Status = NO_REV_RGB_CMD;
    }
}

/**
  * @brief  VICON喂狗处理程序100ms放入定时器
  * @param  None
  * @retval None
  */
void FeedDog_VICON_Pro_100ms(void)
{
    if(FeedDogVICONTimeCount++ > RGB_FEED_DOG_TIME)
    {
        FeedDogVICONTimeCount = 0;
        SensorStatus.VICON_Status = SENSOR_ERROR;
    }
}
